<?php
/*!
 * Medoo database framework
 * https://medoo.in
 * Version 1.7.10
 *
 * Copyright 2020, <PERSON>
 * Released under the MIT license
 */

namespace util\medoo;

use PDO;
use Exception;
use PDOException;
use InvalidArgumentException;

require_once __DIR__ . "/dataConfusion.php";

class Raw {
    public $map;
    public $value;
}

class Medoo
{
    public $pdo;

    protected $type;

    protected $prefix;

    protected $statement;

    protected $dsn;

    protected $logs = [];

    protected $logging = false;

    protected $debug_mode = false;

    protected $guid = 0;

    protected $errorInfo = null;

    protected $dataConfusion = null;

    public function __construct(array $options)
    {
        $host = $options['server'];
        $port = $options['port'];
        $dbname = $options['database_name'];
        $this->dataConfusion = \util\dataConfusion\DataConfusion::getInstance();

        try {
            $mysqlConnString = "mysql:host=$host;port=$port;dbname=$dbname";
            $this->pdo = new PDO($mysqlConnString, $options['username'], $options['password']);
            $this->pdo->exec('set names utf8');
            $this->pdo->exec('SET SQL_MODE=ANSI_QUOTES');
            $this->pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        }
        catch (PDOException $e) {
            throw new PDOException($e->getMessage());
        }
    }

    public function exec($query, $map = [])
    {
        $this->statement = null;

        $statement = $this->pdo->prepare($query);

        $this->statement = $statement;

        foreach ($map as $key => $value) {
            $statement->bindValue($key, $value[ 0 ], $value[ 1 ]);
        }

        $execute = $statement->execute();

        $this->errorInfo = $statement->errorInfo();

        if (!$execute) {
            $this->statement = null;
        }

        return $statement;
    }

    public static function raw($string, $map = [])
    {
        $raw = new Raw();
        $raw->map = $map;
        $raw->value = $string;
        return $raw;
    }

    protected function isRaw($object)
    {
        return $object instanceof Raw;
    }

    protected function buildRaw($raw, &$map)
    {
        if (!$this->isRaw($raw))
        {
            return false;
        }

        $query = preg_replace_callback(
            '/(([`\']).*?)?((FROM|TABLE|INTO|UPDATE|JOIN)\s*)?\<(([a-zA-Z0-9_]+)(\.[a-zA-Z0-9_]+)?)\>(.*?\2)?/i',
            function ($matches)
            {
                if (!empty($matches[ 2 ]) && isset($matches[ 8 ]))
                {
                    return $matches[ 0 ];
                }

                if (!empty($matches[ 4 ]))
                {
                    return $matches[ 1 ] . $matches[ 4 ] . ' ' . $this->tableQuote($matches[ 5 ]);
                }

                return $matches[ 1 ] . $this->columnQuote($matches[ 5 ]);
            },
            $raw->value);

        $raw_map = $raw->map;

        if (!empty($raw_map))
        {
            foreach ($raw_map as $key => $value)
            {
                $map[ $key ] = $this->typeMap($value, gettype($value));
            }
        }

        return $query;
    }

    protected function tableQuote($table)
    {
        if (!preg_match('/^[a-zA-Z0-9_]+$/i', $table))
        {
            throw new InvalidArgumentException("Incorrect table name \"$table\"");
        }

        return '"' . $this->prefix . $table . '"';
    }

    protected function mapKey()
    {
        return ':MeDoO_' . $this->guid++ . '_mEdOo';
    }

    protected function typeMap($value, $type)
    {
        $map = [
            'NULL' => PDO::PARAM_NULL,
            'integer' => PDO::PARAM_INT,
            'double' => PDO::PARAM_STR,
            'boolean' => PDO::PARAM_BOOL,
            'string' => PDO::PARAM_STR,
            'object' => PDO::PARAM_STR,
            'resource' => PDO::PARAM_LOB
        ];

        if ($type === 'boolean')
        {
            $value = ($value ? '1' : '0');
        }
        elseif ($type === 'NULL')
        {
            $value = null;
        }

        return [$value, $map[ $type ]];
    }

    protected function columnQuote($string)
    {
        if (!preg_match('/^[a-zA-Z0-9_]+(\.?[a-zA-Z0-9_]+)?$/i', $string)) {
            throw new InvalidArgumentException("Incorrect column name \"$string\"");
        }

        if (strpos($string, '.') !== false) {
            return '"' . $this->prefix . str_replace('.', '"."', $string) . '"';
        }

        return '"' . $string . '"';
    }

    protected function columnPush(&$columns, &$map, $root, $is_join = false)
    {
        if ($columns === '*')
        {
            return $columns;
        }

        $stack = [];

        if (is_string($columns))
        {
            $columns = [$columns];
        }

        foreach ($columns as $key => $value)
        {
            if (!is_int($key) && is_array($value) && $root && count(array_keys($columns)) === 1)
            {
                $stack[] = $this->columnQuote($key);

                $stack[] = $this->columnPush($value, $map, false, $is_join);
            }
            elseif (is_array($value))
            {
                $stack[] = $this->columnPush($value, $map, false, $is_join);
            }
            elseif (!is_int($key) && $raw = $this->buildRaw($value, $map))
            {
                preg_match('/(?<column>[a-zA-Z0-9_\.]+)(\s*\[(?<type>(String|Bool|Int|Number))\])?/i', $key, $match);

                $stack[] = $raw . ' AS ' . $this->columnQuote($match[ 'column' ]);
            }
            elseif (is_int($key) && is_string($value))
            {
                if ($is_join && strpos($value, '*') !== false)
                {
                    throw new InvalidArgumentException('Cannot use table.* to select all columns while joining table');
                }

                preg_match('/(?<column>[a-zA-Z0-9_\.]+)(?:\s*\((?<alias>[a-zA-Z0-9_]+)\))?(?:\s*\[(?<type>(?:String|Bool|Int|Number|Object|JSON))\])?/i', $value, $match);

                if (!empty($match[ 'alias' ]))
                {
                    $stack[] = $this->columnQuote($match[ 'column' ]) . ' AS ' . $this->columnQuote($match[ 'alias' ]);

                    $columns[ $key ] = $match[ 'alias' ];

                    if (!empty($match[ 'type' ]))
                    {
                        $columns[ $key ] .= ' [' . $match[ 'type' ] . ']';
                    }
                }
                else
                {
                    $stack[] = $this->columnQuote($match[ 'column' ]);
                }
            }
        }

        return implode(',', $stack);
    }

    protected function arrayQuote($array)
    {
        $stack = [];

        foreach ($array as $value)
        {
            $stack[] = is_int($value) ? $value : $this->pdo->quote($value);
        }

        return implode(',', $stack);
    }

    protected function innerConjunct($data, $map, $conjunctor, $outer_conjunctor)
    {
        $stack = [];

        foreach ($data as $value)
        {
            $stack[] = '(' . $this->dataImplode($value, $map, $conjunctor) . ')';
        }

        return implode($outer_conjunctor . ' ', $stack);
    }

    protected function dataImplode($data, &$map, $conjunctor)
    {
        $stack = [];

        foreach ($data as $key => $value)
        {
            $type = gettype($value);

            if (
                $type === 'array' &&
                preg_match("/^(AND|OR)(\s+#.*)?$/", $key, $relation_match)
            )
            {
                $relationship = $relation_match[ 1 ];

                $stack[] = $value !== array_keys(array_keys($value)) ?
                    '(' . $this->dataImplode($value, $map, ' ' . $relationship) . ')' :
                    '(' . $this->innerConjunct($value, $map, ' ' . $relationship, $conjunctor) . ')';

                continue;
            }

            $map_key = $this->mapKey();

            if (
                is_int($key) &&
                preg_match('/([a-zA-Z0-9_\.]+)\[(?<operator>\>\=?|\<\=?|\!?\=)\]([a-zA-Z0-9_\.]+)/i', $value, $match)
            )
            {
                $stack[] = $this->columnQuote($match[ 1 ]) . ' ' . $match[ 'operator' ] . ' ' . $this->columnQuote($match[ 3 ]);
            }
            else
            {
                preg_match('/([a-zA-Z0-9_\.]+)(\[(?<operator>\>\=?|\<\=?|\!|\<\>|\>\<|\!?~|REGEXP)\])?/i', $key, $match);
                $column = $this->columnQuote($match[ 1 ]);

                if (isset($match[ 'operator' ]))
                {
                    $operator = $match[ 'operator' ];

                    if (in_array($operator, ['>', '>=', '<', '<=']))
                    {
                        $condition = $column . ' ' . $operator . ' ';

                        if (is_numeric($value))
                        {
                            $condition .= $map_key;
                            $map[ $map_key ] = [$value, is_float($value) ? PDO::PARAM_STR : PDO::PARAM_INT];
                        }
                        elseif ($raw = $this->buildRaw($value, $map))
                        {
                            $condition .= $raw;
                        }
                        else
                        {
                            $condition .= $map_key;
                            $map[ $map_key ] = [$value, PDO::PARAM_STR];
                        }

                        $stack[] = $condition;
                    }
                    elseif ($operator === '!')
                    {
                        switch ($type)
                        {
                            case 'NULL':
                                $stack[] = $column . ' IS NOT NULL';
                                break;

                            case 'array':
                                $placeholders = [];

                                foreach ($value as $index => $item)
                                {
                                    $stack_key = $map_key . $index . '_i';

                                    $placeholders[] = $stack_key;
                                    $map[ $stack_key ] = $this->typeMap($item, gettype($item));
                                }

                                $stack[] = $column . ' NOT IN (' . implode(', ', $placeholders) . ')';
                                break;

                            case 'object':
                                if ($raw = $this->buildRaw($value, $map))
                                {
                                    $stack[] = $column . ' != ' . $raw;
                                }
                                break;

                            case 'integer':
                            case 'double':
                            case 'boolean':
                            case 'string':
                                $stack[] = $column . ' != ' . $map_key;
                                $map[ $map_key ] = $this->typeMap($value, $type);
                                break;
                        }
                    }
                    elseif ($operator === '~' || $operator === '!~')
                    {
                        if ($type !== 'array')
                        {
                            $value = [ $value ];
                        }

                        $connector = ' OR ';
                        $data = array_values($value);

                        if (is_array($data[ 0 ]))
                        {
                            if (isset($value[ 'AND' ]) || isset($value[ 'OR' ]))
                            {
                                $connector = ' ' . array_keys($value)[ 0 ] . ' ';
                                $value = $data[ 0 ];
                            }
                        }

                        $like_clauses = [];

                        foreach ($value as $index => $item)
                        {
                            $item = strval($item);

                            if (!preg_match('/(\[.+\]|[\*\?\!\%#^-_]|%.+|.+%)/', $item))
                            {
                                $item = '%' . $item . '%';
                            }

                            $like_clauses[] = $column . ($operator === '!~' ? ' NOT' : '') . ' LIKE ' . $map_key . 'L' . $index;
                            $map[ $map_key . 'L' . $index ] = [$item, PDO::PARAM_STR];
                        }

                        $stack[] = '(' . implode($connector, $like_clauses) . ')';
                    }
                    elseif ($operator === '<>' || $operator === '><')
                    {
                        if ($type === 'array')
                        {
                            if ($operator === '><')
                            {
                                $column .= ' NOT';
                            }

                            $stack[] = '(' . $column . ' BETWEEN ' . $map_key . 'a AND ' . $map_key . 'b)';

                            $data_type = (is_numeric($value[ 0 ]) && is_numeric($value[ 1 ])) ? PDO::PARAM_INT : PDO::PARAM_STR;

                            $map[ $map_key . 'a' ] = [$value[ 0 ], $data_type];
                            $map[ $map_key . 'b' ] = [$value[ 1 ], $data_type];
                        }
                    }
                    elseif ($operator === 'REGEXP')
                    {
                        $stack[] = $column . ' REGEXP ' . $map_key;
                        $map[ $map_key ] = [$value, PDO::PARAM_STR];
                    }
                }
                else
                {
                    switch ($type)
                    {
                        case 'NULL':
                            $stack[] = $column . ' IS NULL';
                            break;

                        case 'array':
                            $placeholders = [];

                            foreach ($value as $index => $item)
                            {
                                $stack_key = $map_key . $index . '_i';

                                $placeholders[] = $stack_key;
                                $map[ $stack_key ] = $this->typeMap($item, gettype($item));
                            }

                            $stack[] = $column . ' IN (' . implode(', ', $placeholders) . ')';
                            break;

                        case 'object':
                            if ($raw = $this->buildRaw($value, $map))
                            {
                                $stack[] = $column . ' = ' . $raw;
                            }
                            break;

                        case 'integer':
                        case 'double':
                        case 'boolean':
                        case 'string':
                            $stack[] = $column . ' = ' . $map_key;
                            $map[ $map_key ] = $this->typeMap($value, $type);
                            break;
                    }
                }
            }
        }

        return implode($conjunctor . ' ', $stack);
    }

    protected function whereClause($where, &$map)
    {
        $where_clause = '';

        if (is_array($where))
        {
            $where_keys = array_keys($where);

            $conditions = array_diff_key($where, array_flip(
                ['GROUP', 'ORDER', 'HAVING', 'LIMIT', 'LIKE', 'MATCH']
            ));

            if (!empty($conditions))
            {
                $where_clause = ' WHERE ' . $this->dataImplode($conditions, $map, ' AND');
            }

            if (isset($where[ 'MATCH' ]) && $this->type === 'mysql')
            {
                $MATCH = $where[ 'MATCH' ];

                if (is_array($MATCH) && isset($MATCH[ 'columns' ], $MATCH[ 'keyword' ]))
                {
                    $mode = '';

                    $mode_array = [
                        'natural' => 'IN NATURAL LANGUAGE MODE',
                        'natural+query' => 'IN NATURAL LANGUAGE MODE WITH QUERY EXPANSION',
                        'boolean' => 'IN BOOLEAN MODE',
                        'query' => 'WITH QUERY EXPANSION'
                    ];

                    if (isset($MATCH[ 'mode' ], $mode_array[ $MATCH[ 'mode' ] ]))
                    {
                        $mode = ' ' . $mode_array[ $MATCH[ 'mode' ] ];
                    }

                    $columns = implode(', ', array_map([$this, 'columnQuote'], $MATCH[ 'columns' ]));
                    $map_key = $this->mapKey();
                    $map[ $map_key ] = [$MATCH[ 'keyword' ], PDO::PARAM_STR];

                    $where_clause .= ($where_clause !== '' ? ' AND ' : ' WHERE') . ' MATCH (' . $columns . ') AGAINST (' . $map_key . $mode . ')';
                }
            }

            if (isset($where[ 'GROUP' ]))
            {
                $GROUP = $where[ 'GROUP' ];

                if (is_array($GROUP))
                {
                    $stack = [];

                    foreach ($GROUP as $column => $value)
                    {
                        $stack[] = $this->columnQuote($value);
                    }

                    $where_clause .= ' GROUP BY ' . implode(',', $stack);
                }
                elseif ($raw = $this->buildRaw($GROUP, $map))
                {
                    $where_clause .= ' GROUP BY ' . $raw;
                }
                else
                {
                    $where_clause .= ' GROUP BY ' . $this->columnQuote($GROUP);
                }

                if (isset($where[ 'HAVING' ]))
                {
                    if ($raw = $this->buildRaw($where[ 'HAVING' ], $map))
                    {
                        $where_clause .= ' HAVING ' . $raw;
                    }
                    else
                    {
                        $where_clause .= ' HAVING ' . $this->dataImplode($where[ 'HAVING' ], $map, ' AND');
                    }
                }
            }

            if (isset($where[ 'ORDER' ]))
            {
                $ORDER = $where[ 'ORDER' ];

                if (is_array($ORDER))
                {
                    $stack = [];

                    foreach ($ORDER as $column => $value)
                    {
                        if (is_array($value))
                        {
                            $stack[] = 'FIELD(' . $this->columnQuote($column) . ', ' . $this->arrayQuote($value) . ')';
                        }
                        elseif ($value === 'ASC' || $value === 'DESC')
                        {
                            $stack[] = $this->columnQuote($column) . ' ' . $value;
                        }
                        elseif (is_int($column))
                        {
                            $stack[] = $this->columnQuote($value);
                        }
                    }

                    $where_clause .= ' ORDER BY ' . implode(',', $stack);
                }
                elseif ($raw = $this->buildRaw($ORDER, $map))
                {
                    $where_clause .= ' ORDER BY ' . $raw;	
                }
                else
                {
                    $where_clause .= ' ORDER BY ' . $this->columnQuote($ORDER);
                }

                if (
                    isset($where[ 'LIMIT' ]) &&
                    in_array($this->type, ['oracle', 'mssql'])
                )
                {
                    $LIMIT = $where[ 'LIMIT' ];

                    if (is_numeric($LIMIT))
                    {
                        $LIMIT = [0, $LIMIT];
                    }
                    
                    if (
                        is_array($LIMIT) &&
                        is_numeric($LIMIT[ 0 ]) &&
                        is_numeric($LIMIT[ 1 ])
                    )
                    {
                        $where_clause .= ' OFFSET ' . $LIMIT[ 0 ] . ' ROWS FETCH NEXT ' . $LIMIT[ 1 ] . ' ROWS ONLY';
                    }
                }
            }

            if (isset($where[ 'LIMIT' ]) && !in_array($this->type, ['oracle', 'mssql']))
            {
                $LIMIT = $where[ 'LIMIT' ];

                if (is_numeric($LIMIT))
                {
                    $where_clause .= ' LIMIT ' . $LIMIT;
                }
                elseif (
                    is_array($LIMIT) &&
                    is_numeric($LIMIT[ 0 ]) &&
                    is_numeric($LIMIT[ 1 ])
                )
                {
                    $where_clause .= ' LIMIT ' . $LIMIT[ 1 ] . ' OFFSET ' . $LIMIT[ 0 ];
                }
            }
        }
        elseif ($raw = $this->buildRaw($where, $map))
        {
            $where_clause .= ' ' . $raw;
        }

        return $where_clause;
    }

    protected function selectContext($table, &$map, $join, &$columns = null, $where = null, $column_fn = null)
    {
        preg_match('/(?<table>[a-zA-Z0-9_]+)\s*\((?<alias>[a-zA-Z0-9_]+)\)/i', $table, $table_match);

        if (isset($table_match[ 'table' ], $table_match[ 'alias' ]))
        {
            $table = $this->tableQuote($table_match[ 'table' ]);

            $table_query = $table . ' AS ' . $this->tableQuote($table_match[ 'alias' ]);
        }
        else
        {
            $table = $this->tableQuote($table);

            $table_query = $table;
        }

        $is_join = false;
        $join_key = is_array($join) ? array_keys($join) : null;

        if (
            isset($join_key[ 0 ]) &&
            strpos($join_key[ 0 ], '[') === 0
        )
        {
            $is_join = true;
            $table_query .= ' ' . $this->buildJoin($table, $join);
        }
        else
        {
            if (is_null($columns))
            {
                if (
                    !is_null($where) ||
                    (is_array($join) && isset($column_fn))
                )
                {
                    $where = $join;
                    $columns = null;
                }
                else
                {
                    $where = null;
                    $columns = $join;
                }
            }
            else
            {
                $where = $columns;
                $columns = $join;
            }
        }

        if (isset($column_fn))
        {
            if ($column_fn === 1)
            {
                $column = '1';

                if (is_null($where))
                {
                    $where = $columns;
                }
            }
            elseif ($raw = $this->buildRaw($column_fn, $map))
            {
                $column = $raw;
            }
            else
            {
                if (empty($columns) || $this->isRaw($columns))
                {
                    $columns = '*';
                    $where = $join;
                }

                $column = $column_fn . '(' . $this->columnPush($columns, $map, true) . ')';
            }
        }
        else
        {
            $column = $this->columnPush($columns, $map, true, $is_join);
        }

        return 'SELECT ' . $column . ' FROM ' . $table_query . $this->whereClause($where, $map);
    }

    protected function buildJoin($table, $join)
    {
        $table_join = [];

        $join_array = [
            '>' => 'LEFT',
            '<' => 'RIGHT',
            '<>' => 'FULL',
            '><' => 'INNER'
        ];

        foreach($join as $sub_table => $relation)
        {
            preg_match('/(\[(?<join>\<\>?|\>\<?)\])?(?<table>[a-zA-Z0-9_]+)\s?(\((?<alias>[a-zA-Z0-9_]+)\))?/', $sub_table, $match);

            if ($match[ 'join' ] !== '' && $match[ 'table' ] !== '')
            {
                if (is_string($relation))
                {
                    $relation = 'USING ("' . $relation . '")';
                }

                if (is_array($relation))
                {
                    // For ['column1', 'column2']
                    if (isset($relation[ 0 ]))
                    {
                        $relation = 'USING ("' . implode('", "', $relation) . '")';
                    }
                    else
                    {
                        $joins = [];

                        foreach ($relation as $key => $value)
                        {
                            $joins[] = (
                                strpos($key, '.') > 0 ?
                                    // For ['tableB.column' => 'column']
                                    $this->columnQuote($key) :

                                    // For ['column1' => 'column2']
                                    $table . '."' . $key . '"'
                            ) .
                            ' = ' .
                            $this->tableQuote(isset($match[ 'alias' ]) ? $match[ 'alias' ] : $match[ 'table' ]) . '."' . $value . '"';
                        }

                        $relation = 'ON ' . implode(' AND ', $joins);
                    }
                }

                $table_name = $this->tableQuote($match[ 'table' ]) . ' ';

                if (isset($match[ 'alias' ]))
                {
                    $table_name .= 'AS ' . $this->tableQuote($match[ 'alias' ]) . ' ';
                }

                $table_join[] = $join_array[ $match[ 'join' ] ] . ' JOIN ' . $table_name . $relation;
            }
        }

        return implode(' ', $table_join);
    }

    protected function columnMap($columns, &$stack, $root)
    {
        if ($columns === '*')
        {
            return $stack;
        }

        foreach ($columns as $key => $value)
        {
            if (is_int($key))
            {
                preg_match('/([a-zA-Z0-9_]+\.)?(?<column>[a-zA-Z0-9_]+)(?:\s*\((?<alias>[a-zA-Z0-9_]+)\))?(?:\s*\[(?<type>(?:String|Bool|Int|Number|Object|JSON))\])?/i', $value, $key_match);

                $column_key = !empty($key_match[ 'alias' ]) ?
                    $key_match[ 'alias' ] :
                    $key_match[ 'column' ];

                if (isset($key_match[ 'type' ]))
                {
                    $stack[ $value ] = [$column_key, $key_match[ 'type' ]];
                }
                else
                {
                    $stack[ $value ] = [$column_key, 'String'];
                }
            }
            elseif ($this->isRaw($value))
            {
                preg_match('/([a-zA-Z0-9_]+\.)?(?<column>[a-zA-Z0-9_]+)(\s*\[(?<type>(String|Bool|Int|Number))\])?/i', $key, $key_match);

                $column_key = $key_match[ 'column' ];

                if (isset($key_match[ 'type' ]))
                {
                    $stack[ $key ] = [$column_key, $key_match[ 'type' ]];
                }
                else
                {
                    $stack[ $key ] = [$column_key, 'String'];
                }
            }
            elseif (!is_int($key) && is_array($value))
            {
                if ($root && count(array_keys($columns)) === 1)
                {
                    $stack[ $key ] = [$key, 'String'];
                }

                $this->columnMap($value, $stack, false);
            }
        }

        return $stack;
    }

    protected function dataMap($data, $columns, $column_map, &$stack, $root, &$result)
    {
        if ($root)
        {
            $columns_key = array_keys($columns);

            if (count($columns_key) === 1 && is_array($columns[$columns_key[0]]))
            {
                $index_key = array_keys($columns)[0];
                $data_key = preg_replace("/^[a-zA-Z0-9_]+\./i", "", $index_key);

                $current_stack = [];

                foreach ($data as $item)
                {
                    $this->dataMap($data, $columns[ $index_key ], $column_map, $current_stack, false, $result);

                    $index = $data[ $data_key ];

                    $result[ $index ] = $current_stack;
                }
            }
            else
            {
                $current_stack = [];
                
                $this->dataMap($data, $columns, $column_map, $current_stack, false, $result);

                $result[] = $current_stack;
            }

            return;
        }

        foreach ($columns as $key => $value)
        {
            $isRaw = $this->isRaw($value);

            if (is_int($key) || $isRaw)
            {
                $map = $column_map[ $isRaw ? $key : $value ];

                $column_key = $map[ 0 ];

                $item = $data[ $column_key ];

                if (isset($map[ 1 ]))
                {
                    if ($isRaw && in_array($map[ 1 ], ['Object', 'JSON']))
                    {
                        continue;
                    }

                    if (is_null($item))
                    {
                        $stack[ $column_key ] = null;
                        continue;
                    }

                    switch ($map[ 1 ])
                    {
                        case 'Number':
                            $stack[ $column_key ] = (double) $item;
                            break;

                        case 'Int':
                            $stack[ $column_key ] = (int) $item;
                            break;

                        case 'Bool':
                            $stack[ $column_key ] = (bool) $item;
                            break;

                        case 'Object':
                            $stack[ $column_key ] = unserialize($item);
                            break;

                        case 'JSON':
                            $stack[ $column_key ] = json_decode($item, true);
                            break;

                        case 'String':
                            $stack[ $column_key ] = $item;
                            break;
                    }
                }
                else
                {
                    $stack[ $column_key ] = $item;
                }
            }
            else
            {
                $current_stack = [];

                $this->dataMap($data, $value, $column_map, $current_stack, false, $result);

                $stack[ $key ] = $current_stack;
            }
        }
    }

    public function select($table, $join, $columns = null, $where = null)
    {
        $map = [];
        $result = [];
        $column_map = [];

        $column = $where === null ? $join : $columns;

        $is_single = (is_string($column) && $column !== '*');

        // 对查找条件进行加密
        $mappingData = [];
        $this->columnsEncode($table, $columns, $mappingData);
       // \util\log\akcsLog::debug("select,table = $table, join = " . json_encode($join) . ",columns = " . json_encode($columns) . ",where = " . json_encode($where));

        $query = $this->exec($this->selectContext($table, $map, $join, $columns, $where), $map);

        $this->columnMap($columns, $column_map, true);

        //\util\log\akcsLog::debug("select,columnMap = " . json_encode($column_map));
        if (!$this->statement)
        {
            return false;
        }

        if ($columns === '*')
        {
            $result = $query->fetchAll(PDO::FETCH_ASSOC);
            
            // 对查询结果进行解密
            $this->columnsDecode($table, $result);
            return $result;
        }

        while ($data = $query->fetch(PDO::FETCH_ASSOC))
        {
            $current_stack = [];

            $this->dataMap($data, $columns, $column_map, $current_stack, true, $result);
            
           // \util\log\akcsLog::debug("select, data = " . json_encode($data));
        }

        if ($is_single)
        {
            $single_result = [];
            $result_key = $column_map[ $column ][ 0 ];

            foreach ($result as $item)
            {
                $single_result[] = $item[ $result_key ];
            }

            return $single_result;
        }
        
        // 对查询结果进行解密
        $this->columnsDecode($table, $result);

        return $result;
    }

    public function insert($table, $datas)
    {
       // \util\log\akcsLog::debug("insert datas :" . json_encode($datas));
        $stack = [];
        $columns = [];
        $fields = [];
        $map = [];
        $mappingData = [];

        // 对插入的数据进行加密
        $this->columnsEncode($table, $datas, $mappingData);

        // 更新mapping表
       // $this->insert2MappingList($mappingData);

        if (!isset($datas[ 0 ]))
        {
            $datas = [$datas];
        }

        foreach ($datas as $data)
        {
            foreach ($data as $key => $value)
            {
                $columns[] = $key;
            }
        }

        $columns = array_unique($columns);

        foreach ($datas as $data)
        {
            $values = [];

            foreach ($columns as $key)
            {
                if ($raw = $this->buildRaw($data[ $key ], $map))
                {
                    $values[] = $raw;
                    continue;
                }

                $map_key = $this->mapKey();

                $values[] = $map_key;

                if (!isset($data[ $key ]))
                {
                    $map[ $map_key ] = [null, PDO::PARAM_NULL];
                }
                else
                {
                    $value = $data[ $key ];

                    $type = gettype($value);

                    switch ($type)
                    {
                        case 'array':
                            $map[ $map_key ] = [
                                strpos($key, '[JSON]') === strlen($key) - 6 ?
                                    json_encode($value) :
                                    serialize($value),
                                PDO::PARAM_STR
                            ];
                            break;

                        case 'object':
                            $value = serialize($value);

                        case 'NULL':
                        case 'resource':
                        case 'boolean':
                        case 'integer':
                        case 'double':
                        case 'string':
                            $map[ $map_key ] = $this->typeMap($value, $type);
                            break;
                    }
                }
            }

            $stack[] = '(' . implode(', ', $values) . ')';
        }

        foreach ($columns as $key)
        {
            $fields[] = $this->columnQuote(preg_replace("/(\s*\[JSON\]$)/i", '', $key));
        }

        return $this->exec('INSERT INTO ' . $this->tableQuote($table) . ' (' . implode(', ', $fields) . ') VALUES ' . implode(', ', $stack), $map);
    }

    public function update($table, $data, $where = null)
    {
        $fields = [];
        $map = [];
        $mappingData = [];
        $mappingDataWhere = [];

       // $this->updateMappingFieldBeforeUpdate($table, $data, $where);

        // 对update的字段进行加密
        $this->columnsEncode($table, $data, $mappingData);
        
        // 对where条件的字段进行加密
        $this->columnsEncode($table, $where, $mappingDataWhere);

        foreach ($data as $key => $value) {
            $column = $this->columnQuote(preg_replace("/(\s*\[(JSON|\+|\-|\*|\/)\]$)/i", '', $key));

            if ($raw = $this->buildRaw($value, $map)) {
                $fields[] = $column . ' = ' . $raw;
                continue;
            }

            $map_key = $this->mapKey();

            preg_match('/(?<column>[a-zA-Z0-9_]+)(\[(?<operator>\+|\-|\*|\/)\])?/i', $key, $match);

            if (isset($match[ 'operator' ]))
            {
                if (is_numeric($value))
                {
                    $fields[] = $column . ' = ' . $column . ' ' . $match[ 'operator' ] . ' ' . $value;
                }
            }
            else
            {
                $fields[] = $column . ' = ' . $map_key;

                $type = gettype($value);

                switch ($type)
                {
                    case 'array':
                        $map[ $map_key ] = [
                            strpos($key, '[JSON]') === strlen($key) - 6 ?
                                json_encode($value) :
                                serialize($value),
                            PDO::PARAM_STR
                        ];
                        break;

                    case 'object':
                        $value = serialize($value);

                    case 'NULL':
                    case 'resource':
                    case 'boolean':
                    case 'integer':
                    case 'double':
                    case 'string':
                        $map[ $map_key ] = $this->typeMap($value, $type);
                        break;
                }
            }
        }

        return $this->exec('UPDATE ' . $this->tableQuote($table) . ' SET ' . implode(', ', $fields) . $this->whereClause($where, $map), $map);
    }

    public function delete($table, $where)
    {
        $map = [];
        $mappingData = [];

        // 更新Mapping表的ref值
     //   $this->updateMappingTableBeforeDelete($table, $where);

        // 对where条件的字段进行加密
        $this->columnsEncode($table, $where, $mappingData);

        return $this->exec('DELETE FROM ' . $this->tableQuote($table) . $this->whereClause($where, $map), $map);
    }

    public function get($table, $join = null, $columns = null, $where = null)
    {
        $map = [];
        $result = [];
        $column_map = [];
        $current_stack = [];

        if ($where === null) {
            $column = $join;
            unset($columns[ 'LIMIT' ]);
        } else {
            $column = $columns;
            unset($where[ 'LIMIT' ]);
        }

        $is_single = (is_string($column) && $column !== '*');

        // 对查找条件进行加密
        $mappingData = [];
        $this->columnsEncode($table, $columns, $mappingData);

        $query = $this->exec($this->selectContext($table, $map, $join, $columns, $where) . ' LIMIT 1', $map);

        if (!$this->statement) {
            return false;
        }

        $data = $query->fetchAll(PDO::FETCH_ASSOC);
        
        // 对查询结果进行解密
        $this->columnsDecode($table, $data);

        if (isset($data[0])) {
            if ($column === '*') {
                return $data[0];
            }

            $this->columnMap($columns, $column_map, true);

            $this->dataMap($data[0], $columns, $column_map, $current_stack, true, $result);

            if ($is_single) {
                return $result[0][$column_map[$column][0]];
            }
            return $result[0];
        }
    }

    public function has($table, $join, $where = null)
    {
        $map = [];
        $column = null;

        $query = $this->exec('SELECT EXISTS(' . $this->selectContext($table, $map, $join, $column, $where, 1) . ')', $map);
        
        if (!$this->statement)
        {
            return false;
        }

        $result = $query->fetchColumn();

        return $result === '1' || $result === 1 || $result === true;
    }

    //插入Mapping表数据
    private function insert2MappingList($mappingData)
    {
      // \util\log\akcsLog::debug("insert2MappingList mappingData = ".json_encode($mappingData));

        $db = \util\container\mappingDb();
        foreach ($mappingData as $fields => $fieldsArray) {
            // 插入数据
            $mappingKeys = [];
            $mappingValues = [];
            
            // 获取mapping库中的表名
            $mappingTable = DB_CONFUSION_MAPPING_TABLES[$fields];

            foreach ($fieldsArray as $column => $value) {
                array_push($mappingKeys, $column);
                array_push($mappingValues, $value);
            }

            $ref = $fieldsArray['Ref'];
            $keys = implode(",", $mappingKeys);
            $values = implode(",", array_map(function($value) {return "'" . addslashes($value) . "'";}, $mappingValues));

            $sql = "insert into $mappingTable ($keys) values ($values) ON DUPLICATE KEY UPDATE Ref = Ref + $ref";
           // \util\log\akcsLog::debug("insert2MappingList sql = " . $sql);
            $sth = $db->prepare($sql);
            $sth->execute();
        }
    }

    private function deleteMappingByData($mappingData)
    {
    //    \util\log\akcsLog::debug("deleteMappingByData mappingData = " . json_encode($mappingData));

        $db = \util\container\mappingDb();
        foreach ($mappingData as $fields => $fieldsArray) {
            // 获取mapping库中的表名
            $mappingTable = DB_CONFUSION_MAPPING_TABLES[$fields];
            
            // 获取encolumn的值
            $enColumn = $fieldsArray["EnColumn"];

            // 获取引用减少次数
            $ref = $fieldsArray["Ref"];

            //\util\log\akcsLog::debug("deleteMappingByData mappingTable = $mappingTable, enColumn = $enColumn, ref = $ref");

            // 减少引用值
            $sql = "update $mappingTable SET Ref = Ref - $ref WHERE EnColumn = '$enColumn'";
            $sth = $db->prepare($sql);
            $sth->execute();
 
            // 删除引用值为0的column
            $sql = "delete from $mappingTable where EnColumn = '$enColumn' and Ref <= 0";
            $sth = $db->prepare($sql);
            $sth->execute();
        }
    }

    // update: 对即将update的字段进行mapping表的ref值修改
    private function updateMappingFieldBeforeUpdate($table, $data, $where)
    {
        // 判断该表是否需要混淆
        if (array_key_exists($table, DB_CONFUSION_FIELDS_MAPPER)) {

            // 判断更新的字段是否为混淆字段
            $updateField = array_keys($data)[0];
            if (in_array($updateField, DB_CONFUSION_FIELDS_MAPPER[$table])) {

                // 获取修改的字段
                $existsData = $this->select($table, ["$updateField"] , $where);
                if ($existsData) {

                    // 先减少修改前的字段的ref
                    $mappingDataBefore = ["$updateField" => ["EnColumn" => $this->dataConfusion->encrypt($existsData[0][$updateField]), "Ref" => count($existsData)]];
                    $this->deleteMappingByData($mappingDataBefore);
  
                    // 再增加修改后的字段的ref
                    $mappingDataAfter = ["$updateField" => ["EnColumn" => $this->dataConfusion->encrypt($data[$updateField]), "DeColumn" => $data[$updateField], "Ref" => count($existsData)]];
                    $this->insert2MappingList($mappingDataAfter);
                }
            }
        }
    }

    // delete: 对即将delete的字段先进行mapping表的ref--
    private function updateMappingTableBeforeDelete($table, $where)
    {
       // \util\log\akcsLog::debug("updateMappingTableBeforeDelete");
        // 判断当前表是否需要混淆
        if (array_key_exists($table, DB_CONFUSION_FIELDS_MAPPER)) {

            // 获取即将要删除的数据
            $deleteColumns = $this->select($table, "*", $where);

            //\util\log\akcsLog::debug("updateMappingTableBeforeDelete deleteColumn = " . json_encode($deleteColumns));

            foreach ($deleteColumns as $row => $deleteColumn) {

                // 获取该表的混淆字段
                $confusionFields = DB_CONFUSION_FIELDS_MAPPER[$table];
                
                // 遍历混淆字段
                $mappingData = [];
                foreach($confusionFields as $field) {
                      // 混淆字段非空
                      if (isset($deleteColumn[$field]) && !is_null($deleteColumn[$field])) {
                        // 插入混淆字段的key value到mappingData中
                        $mappingData[$field] = ['EnColumn' =>  $this->dataConfusion->encrypt($deleteColumn[$field]), 'Ref' => 1];
                    }
                }
        
                // 更新混淆字段的mapping ref值
                $this->deleteMappingByData($mappingData);
            }
        }
    }

    private function columnsEncode($table, &$columns, &$mappingData)
    {
       // \util\log\akcsLog::debug("columnsEncode table = $table, condition columns =" . json_encode($columns));

        if (array_key_exists($table, DB_CONFUSION_FIELDS_MAPPER)) {
            $confusionField = DB_CONFUSION_FIELDS_MAPPER[$table];

            foreach ($columns as $column => $value) {

                // 检查该列是否需要混淆（加密）
                if (in_array($column, $confusionField)) {
                    // 这里调用加密方法，返回加密后的值
                    $encryptedValue = $this->dataConfusion->encrypt($value);

                    // 插入mappingData中
                    $mappingData[$column] = ['EnColumn' => $encryptedValue, 'DeColumn' => $value, 'Ref' => 1];

                    // 更新 $columns 数组中的值为加密后的值
                    $columns[$column] = $encryptedValue;

                    //\util\log\akcsLog::debug("columnsEncode table = $table , column = $column , valueBefore = $value, valuAfter = $columns[$column]");
                }
            }
        }
    }

    private function columnsDecode($table, &$columns)
    {
       // \util\log\akcsLog::debug("resultDecode begin, table = $table, result = " . json_encode($columns));

        if (array_key_exists($table, DB_CONFUSION_FIELDS_MAPPER)) {
            // 获取table对应的加密字段
            $confusionFields = DB_CONFUSION_FIELDS_MAPPER[$table];
           // \util\log\akcsLog::debug("resultDecode table = $table , confusionFields = " . json_encode($confusionFields));

            // 遍历结果集中的每一条记录
            foreach ($columns as &$row) {
                foreach ($confusionFields as $field) {
                    // 检查字段是否存在且不为null
                    if (isset($row[$field]) && !is_null($row[$field])) {

                        //\util\log\akcsLog::debug("Before decryption, $field = " . $row[$field]);

                        // 解密操作
                        $decryptedValue = $this->dataConfusion->decrypt($row[$field]);

                        // 更新数组中的字段为解密后的值
                        $row[$field] = $decryptedValue;

                        // 日志记录解密后的值
                      //  \util\log\akcsLog::debug("After decryption, $field: " . $row[$field]);
                    }
                }
                unset($row);
            }
        }
    //    \util\log\akcsLog::debug("resultDecode end, table = $table, result = " . json_encode($columns));
    }
}