<?php
require_once(__DIR__ . '/../service/common/model/personalAccount.php');

class UserDataMiddle
{
    public function __invoke($request, $response, $next)
    {
        if(\util\common\isSpecialReq($request))
        {
            return $next($request, $response);
        }

        $userData = \util\container\getUserData();
        $projectType = $userData['ProjectType'];
        $account = $userData['Account'];

        if($projectType == PROJECT_TYPE_OFFICE || $projectType == PROJECT_TYPE_NEW_OFFICE) {
            $data = \common\model\getOfficeCommInfo($account);
        } else {
            $data = \common\model\getCommInfo($account);
        }
        \util\container\setUserData($data);

        \util\log\akcsLog::debug("userinfo : [account] = {account}, role = {role}, UserInfoUUID = {UserInfoUUID}, UUID = {UUID}", 
                                ['account' => $data['UserAccount'], 'role' => $data['Role'], 'UserInfoUUID' => $data['UserInfoUUID'], 'UUID' => $data['UUID']]);

        $response = $next($request, $response);
        
        return $response;
    }
}
