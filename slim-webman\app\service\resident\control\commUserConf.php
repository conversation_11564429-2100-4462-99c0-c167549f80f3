<?php

namespace  resident\control;

require_once __DIR__ . "/../../common/model/distributorInfo.php";
require_once __DIR__ . "/../../common/model/personalAccountCnf.php";

//和用户无关，固定写死的配置
function getCommAppConstConf()
{
    $constConf = [];
    $constConf['show_payment'] = 0;
    $constConf['show_subscription'] = 0;
    $constConf['sip_server'] = G_PBX_IPV4;
    $constConf['sip_server_ipv6'] = G_PBX_IPV6;
    $constConf['video_res']  = "2";
    $constConf['video_bitrate'] = "512";
    $constConf['video_storage_time'] = "0";
    $constConf['have_public_dev'] = "1";
    $constConf['data_collection'] = 0; //app数据收集到网管系统 后续如果用户较多可采样收集
    return $constConf;
}
//直接从数据容器user_data中就能取到的配置
function getCommAppUserDataConf()
{
    $userDataConf = [];
    $userData = \util\container\getUserData(); 
    $userDataConf['sip'] = $userData['SipAccount'];
    $userDataConf['sip_passwd'] = \util\utility\passwdDecode($userData['SipPwd']);
    $userDataConf['display_name'] = $userData['Name'];
    $userDataConf['uid'] = $userData['UserAccount'];
    $userDataConf['uuid'] = $userData['UUID'];
    $userDataConf['node'] = $userData['Account'];
    $userDataConf['codec'] = strval($userData['Codec']); //0=PCMU, 8 =PCMA, 18=G.729 用逗号隔开代表优先级 18,0,8。如果值空代表默认或客户端自行定义
    $userDataConf['role'] = intval($userData['Role']);
    $userDataConf['motion_alert'] = \util\redisManage\getAppMotionStatus($userData['UserAccount']); //app-uid的motion接收开发
    return $userDataConf;
}

//用户开关相关配置
function getCommAppSwitchConf($communityInfo)
{
    $switchConf = [];
    $userData = \util\container\getUserData(); 

    $switch = intval($userData['UserSwitch']);
    $pinInit = \util\utility\switchHandle($switch, PersonlAccountSwitchPinInit);
    $enableConfirmFlag = \util\utility\switchHandle($switch, PersonlAccountSwitchConfirm);   
    //高级功能相关
    $featureExpire = \resident\model\checkFeaturePlanIsExpire($userData['MngID']);
    $showFace = \common\model\checkShowFace($userData, $featureExpire);
    $isShowTmpkey = \common\model\checkShowTmpkey($userData, $featureExpire);
    $enableThirdCamera = \common\model\checkEnableThirdCamera($userData['MngID'], $featureExpire);
    $isShowIDAccess = \common\model\checkShowIDAccess($userData, $featureExpire);
    $enableCreateRF = \common\model\checkEnableCreateRFCard($userData, $featureExpire);
    //社区switch相关
    $isNewComm = $communityInfo['IsNew'];
    $enablePinConfig = \common\model\checkEnablePinConfig($userData, $communityInfo['Switch'], $featureExpire);
    //pinInit字段也需要判断是否允许创建pin,1-已初始化；0-未初始化
    $pinInit = $enablePinConfig ? $pinInit : 1;

    $switchConf['enable_pin_config'] = intval($enablePinConfig);
    $switchConf['show_tempkey'] = intval($isShowTmpkey);
    $switchConf['show_face'] = intval($showFace); //人脸识别开关
    $switchConf['enable_confirm_flag'] = intval($enableConfirmFlag);  //高级设置开关
    $switchConf['enable_third_camera'] = intval($enableThirdCamera);

    $switchConf['community_contact'] = \common\model\getCommunityContactSwitch($userData);

    $distributorInfo = \common\model\getDistributorInfoByMngUUID($userData['MngUUID']);
    $switchConf['show_landline'] = getCommAppEnableLandlineService($distributorInfo, $communityInfo);
    $switchConf['enable_smarthome'] = intval(getCommunityAptEnabelSmartHome($distributorInfo, $communityInfo));

    if (\util\version\isNeedReturnPinInit()) {
        $switchConf['pin_init'] = intval($pinInit); //pin初始化标记
    }
    $switchConf['show_id_access'] = intval($isShowIDAccess & $isNewComm);
    $switchConf['show_thirdparty_lock'] = $isNewComm ? "1" : "0";
    $switchConf['show_pm'] = $isNewComm ? "1" : "0"; //只有新社区的主从账号需要展示Property Manager开关
    $switchConf['is_old_community'] = $isNewComm ? 0 : 1; 

    // 新社区的社区只有主账号展示创建RF卡开关，从账号不展示
    if($isNewComm && $userData['Role'] == ROLE_TYPE_COMMUNITY_MASTER){
        $switchConf['enable_create_rf'] = intval($enableCreateRF);
    }

    return $switchConf;
}

//transtype相关
function getCommAppTransTypeConf()
{
    $transTypeConf = [];
    $userData = \util\container\getUserData(); 

    $transType = $userData['SipType'];
    $mngSipType = \common\model\getUserMngSipType($userData['Role'], $userData['ParentID']);
    if ($mngSipType["SipType"] != APP_SIP_TYPE_NONE) {
        $transType = $mngSipType["SipType"];
    }

    $transTypeConf['trans_type'] = \util\common\transSipType($userData['UUID'], $transType);
    $transTypeConf['rtp_confuse'] = intval($mngSipType["RtpConFuse"]);

    return $transTypeConf;
}

//落地相关
function getCommAppLandlineConf()
{
    $landlineConf = [];
    $userData = \util\container\getUserData(); 
    $landlineConf['landline'] = \util\common\getPbxLandlineNumber($userData['PhoneCode'], $userData['Phone'], $userData['Phone2'], $userData['Phone3']);
    return $landlineConf;
}
    
//多套房相关
function getCommAppMultiSiteConf($appLandLineConf)
{
    $multiSiteConf = []; 
    $userData = \util\container\getUserData(); 

    // 主站点的sip账号密码
    $userInfo = \common\model\getUserInfoByUUID($userData['UserInfoUUID']);
    $mainSipInfo = \common\model\getUserSipInfo($userInfo['AppMainUserAccount']);
    $multiSiteConf['main_sip'] = $userInfo['AppMainUserAccount'];
    $multiSiteConf['main_sip_passwd'] = \util\utility\passwdDecode($mainSipInfo['SipPwd']);
    // 是否为多套房用户
    $multiSiteConf['is_site_link'] = \common\control\checkIsMultiSiteUser();
    if ($multiSiteConf['is_site_link']) {
        $allLandlines = [];
        $personalAccountList = \common\model\getAllSiteInfoByUserInfoUUID($userData['UserInfoUUID']);
        foreach ($personalAccountList as $siteInfo) {
            $siteLandline = \util\common\getPbxLandlineNumber($siteInfo['PhoneCode'], $siteInfo['Phone'], $siteInfo['Phone2'], $siteInfo['Phone3']);
            $allLandlines = array_merge($allLandlines, $siteLandline);
        }
        //app根据all_sites_landline添加app联系人，根据show_landline和landline字段判断是否弹窗，如果all_sites_landline有变化也需要弹窗
        $multiSiteConf['all_sites_landline'] = array_values(array_unique($allLandlines));
    } else {
        $multiSiteConf['all_sites_landline'] = $appLandLineConf;
    }
    
    $siteinfo = [];
    \common\control\getCommSiteInfo($userData, $siteinfo);
    $multiSiteConf['room_name'] = $siteinfo['room_name'];
    $multiSiteConf['project_name'] = $siteinfo['project_name'];
    $multiSiteConf['room_title'] = $siteinfo['room_title'];

    return $multiSiteConf;
}

//过期处理相关
function getCommAppExpireConf()
{
    $expireConf = []; 
    $userData = \util\container\getUserData(); 
    \common\model\checkAccountExpire2($expireConf, $userData);
    return $expireConf;
}

function getCommAppConf($checkDev, $checkSlave, $communityInfo)
{
    //顺序不能调整，否则可能出现覆盖错误问题，比如过期的配置判断必须放最后
    $appConf = [];
    $appConf['check_dev'] = intval($checkDev); //检查室内机的收费方案
    $appConf['check_slave'] = intval($checkSlave); //检查家庭成员控制方案
    $appConf = array_merge($appConf, getCommAppConstConf());
    $appConf = array_merge($appConf, getCommAppUserDataConf());
    $appConf = array_merge($appConf, getCommAppSwitchConf($communityInfo));
    $appConf = array_merge($appConf, getCommAppTransTypeConf());
    $appConf = array_merge($appConf, getCommAppLandlineConf());
    $appConf = array_merge($appConf, getCommAppMultiSiteConf($appConf['landline']));
    $appConf = array_merge($appConf, getCommAppExpireConf());
    
    return $appConf;
}

function getCommAppUnreadMsg()
{
    $unreadData = [];
    $userData = \util\container\getUserData(); 
    $unreadData['messages_num'] = intval(\common\model\getMessagesNumV65());
    $unreadData['activities_num'] = intval(\common\model\getActivitiesNum($userData));
    return $unreadData;
}

function getCommAppThirdDev($accessDevs)
{
    $userData = \util\container\getUserData(); 
    $thirdPartyDevList = [];

    \common\model\getThirdPartyDevList($userData, $thirdPartyDevList);
    \common\model\getThirdPartyCameraList($userData, $thirdPartyDevList, $accessDevs);
    // dormakaba锁
    $dormakabaLockList = \common\control\getDormakabaLockList($accessDevs);
    // salto 锁
    $saltoLockList = \common\control\salto\getCommunityEnduserSaltoLockList($accessDevs);
    $thirdPartyDevList["lock_dev_list"] = array_merge($thirdPartyDevList["lock_dev_list"], $saltoLockList);
    $thirdPartyDevList["lock_dev_list"] = array_merge($thirdPartyDevList["lock_dev_list"], $dormakabaLockList["lock_dev_list"]);
    
    if(\util\version\isShowNewBrandLock())
    {
        //ITec锁
        $itecLockList = \common\control\iTec\getCommunityEnduserLockList();
        $thirdPartyDevList["lock_dev_list"] = array_merge($thirdPartyDevList["lock_dev_list"], $itecLockList);
        //TT锁
        $ttLockList = \common\control\TTLock\getEnduserLockList();
        $thirdPartyDevList["lock_dev_list"] = array_merge($thirdPartyDevList["lock_dev_list"], $ttLockList);
    }

    // NVR
    $nvrList = \common\control\thirdNvr\getComAptThirdNvrList();
    $thirdPartyDevList["third_nvr_list"] = $nvrList;

    return $thirdPartyDevList;
}

function getCommUserConfV65($response)
{
    $userData = \util\container\getUserData();
    $version = \util\container\getApiVersion();
    \util\log\akcsLog::debug("(floatval)api-version:" . floatval($version));
    $communityInfo = \resident\model\getCommunityInfo($userData['MngID']);
    $aptMacs = [];//家庭设备列表
    $accessDevs = [];//用户有权限操作的所有设备列表
    if($communityInfo['IsNew'])
    {
        $devList = \resident\model\getNewCommDevicesList($aptMacs, $accessDevs);
    }
    else
    {
        $devList = \resident\model\getOldCommDevicesList($aptMacs, $accessDevs);
    }


    //室内机方案,室内机是否上线标识
    $checkDev = \common\model\checkIndoorPayPlan($aptMacs, $userData['Role'], $userData['Account']);
    if (\util\version\isNoSupportCheckDev() && $checkDev == 0) {
        //旧版本app必须在这里直接拦截
        \util\log\akcsLog::debug("getCommUserConfV64 checkIndoorPayPlan false. uid = {$userData['UserAccount']}");
        return \util\response\setResponseMessage($response, ERR_CODE_FAILED, [] ,"the token does not exist");
    }
    //判断高级功能,家庭成员账号数量限制
    $checkSlave = 1;
    if ($userData['Role'] == ROLE_TYPE_COMMUNITY_SLAVE) {
        //根据Account查找APPSpecial中是否存在该从账号
        $featureExpire = \resident\model\checkFeaturePlanIsExpire($userData['MngID']);
        $checkSlave = \common\model\checkFamilyMemberControl($userData['MngID'], $userData['UserAccount'], $featureExpire);
        if (\util\version\isNoSupportCheckDev() && $checkSlave == 0) {
            //旧版本app必须在这里直接拦截
            \util\log\akcsLog::debug("getCommUserConfV64 checkFamilyMemberControl false. uid = {$userData['UserAccount']}");
            return \util\response\setResponseMessage($response, ERR_CODE_FAILED, [] ,"the token does not exist");
        }
    }
 
    $datas = [
        "app_conf" => getCommAppConf($checkDev, $checkSlave, $communityInfo),
        "dev_list" => $devList,
        "third_party_dev_list" => getCommAppThirdDev($accessDevs),
        "unread_msg" => getCommAppUnreadMsg()
    ];

    return \util\response\setResponseMessage($response, ERR_CODE_SUCCESS, $datas);  
}

// 判断apt是否开启落地功能
function getCommunityAppEnableLandlineService($distributorInfo, $communityInfo)
{
    $enableLandline = 0;
    $userData = \util\container\getUserData(); 

    // dis 没配置按 apt 收费，看社区的配置
    // dis 配置了按 apt 收费，看apt的配置
    if ($distributorInfo['IsEnableAptChargePlan']) {
        // 判断Apt是否开启落地
        $enableLandline = \common\model\getEnableLandlineService($userData['Account']);
    } else {
        // 判断社区是否开启落地
        $enableLandline = \util\utility\switchHandle($communityInfo['Switch'], DevSwitchEnableLandline);
    }
    return $enableLandline;
}

// 是否开启社区Apt家居开关
function getCommunityAptEnabelSmartHome($distributorInfo, $communityInfo)
{
    $enableSmartHome = 0;
    $userData = \util\container\getUserData();

    // dis 没配置按 apt 收费，看社区的配置
    // dis 配置了按 apt 收费，看apt的配置
    if ($distributorInfo['IsEnableAptChargePlan']) {
        // 判断Apt是否开启家居开关
        $enableSmartHome = \common\model\getPersonalAccountCnf($userData['Account']);
    } else {   
        // 判断社区是否开启家居开关
        $enableSmartHome = \util\utility\switchHandle($communityInfo['Switch'], CommunitySwitchEnableSmartHome);
    }
    return $enableSmartHome;
}