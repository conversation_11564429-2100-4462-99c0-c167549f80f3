#!/bin/bash
# ****************************************************************************
# Author        :   buhuai.su
# Last modified :   2022-08-05
# Filename      :   install.sh
# Version       :
# Description   :   start.sh 启动脚本
# Modifier      :
# ****************************************************************************

set -euo pipefail

export ETCDCTL_API=3

RSYNC_PATH=$1              #代码同步到的目录
PROJECT_RUN_PATH=$2        #项目运行路径
MIDDLEWARE=$3
ENV=$4
HOST=$5
HOSTNAME=$9


if [ "$MIDDLEWARE" == "slim_backend_adapt" ];then
    bash -x "$RSYNC_PATH"/shell/start_adapt.sh "$@"
    exit
fi

# Input:
#   $1: item
#   $2: conf_file
# Output:
#   value: the value of item
grep_conf() {
    grep -w "$1" "$2" | cut -d'=' -f 2 | awk '{gsub(/^[ \t]+|[ \t]+$/, "");print}'
}


create_php_config() {
    PHP_CONFIG=$1

    OFFLINE_TEMP_KEY='const SUPPORT_OFFLINE_TEMP_KEY_COMMUNITYS = array(0);'
    # 1=ccloud/2=scloud/3=ecloud/4=ucloud/5=other
    # 区分国内还是国外环境
    if [ "$SYSTEM_AREA" -eq 1 ];then
        SERVER_TYPE=cn
        isDisplayPhone=0
        SHOWPHONE=1
        CANGETTOOLBOX=0
        OFFLINE_TEMP_KEY='const SUPPORT_OFFLINE_TEMP_KEY_COMMUNITYS = array(424);'
    elif [ "$SYSTEM_AREA" -eq 2 ];then
        SERVER_TYPE=as
        isDisplayPhone=1
        SHOWPHONE=0
        CANGETTOOLBOX=0
    elif [ "$SYSTEM_AREA" -eq 3 ];then
        SERVER_TYPE=eu
        isDisplayPhone=1
        SHOWPHONE=0
	CANGETTOOLBOX=1
    elif [ "$SYSTEM_AREA" -eq 4 ];then
        SERVER_TYPE=na
        isDisplayPhone=1
        SHOWPHONE=0
        CANGETTOOLBOX=1
    elif [ "$SYSTEM_AREA" -eq 6 ];then
        SERVER_TYPE=ru
        isDisplayPhone=1
        SHOWPHONE=0
        CANGETTOOLBOX=0
    elif [ "$SYSTEM_AREA" -eq 7 ];then
        SERVER_TYPE=jp
        isDisplayPhone=1
        SHOWPHONE=0
        CANGETTOOLBOX=0  
    elif [ "$SYSTEM_AREA" -eq 8 ];then
        SERVER_TYPE=au
        isDisplayPhone=1
        SHOWPHONE=0
        CANGETTOOLBOX=0          
    else #5是others
        SERVER_TYPE=ot
        isDisplayPhone=1
        SHOWPHONE=0
        CANGETTOOLBOX=0
    fi

cat > $PHP_CONFIG <<-EOF
<?php

const SERVERURL = '${GATE_IP}:9999';
const SERVERNUMBER = '${GATEWAY_NUM}';
const SERVER_NUMBER_EXPAND = [$GATEWAY_NUM, ${EXPAND_GATEWAY_NUM}];
const IPV4IMG = 'https://${WEB_DOMAIN}:8091';
const IPV6IMG = 'https://[${WEB_IPV6}]:8091';
const IPV4VIDEO = 'http://${CSVSIP}:8188';
const IPV6VIDEO = 'http://[${CSVSIPV6}]:8188';
const SERVERHOST = 'https://${WEB_DOMAIN}';
const SIPSERVER = 'http://:118';
const G_PBX_IPV6 = "[${PBX_OUTER_IPV6}]:5070";
const G_PBX_IPV4 = "${PBX_OUTER_IPV4}:5070";

#处理二维码图片url的服务器地址
const WEB_IP = '${WEB_IP}';
const CSGAET_NET = '${GATE_IP}:9999';
const CSGAET_NET_IPV6 = '[${GATE_IPV6}]:19999';
const CSGAET_DOMAIN = '${GATE_DOMAIN_NAME}:9999';
const WEB_DOMAIN = '${WEB_DOMAIN}';

#bm
const BMURL = 'https://${BM_IP}/bmserver/';
const BMAPYURL = 'https://${BM_DOMAIN}/dist/pay.html';

const CANGETTOOLBOX = $CANGETTOOLBOX;
const SERVER_LOCATION = "$SERVER_TYPE";

#db
const AKCS_DATABASEIP="$AKCS_DATABASEIP";
const AKCS_DATABASEPORT = $AKCS_DATABASEPORT;
const LOG_DATABASEIP="$LOG_DATABASEIP";
const LOG_DATABASEPORT = $LOG_DATABASEPORT;
const DATABASEPWD = "$DECRYPTED_DB_PASSWORD";

#redis
const REDISIP = "${REDIS_INNER_IP}";
const ENABLE_REDIS_SENTINEL = "$ENABLE_REDIS_SENTINEL";
const REDIS_SENTINEL_HOSTS = "$SENTINEL_HOSTS";

#tmpkey
$OFFLINE_TEMP_KEY

#smart_home
const ABLE_SMART_HOME = $SMARTHOME_ENABLE;
const SMART_HOME_TOKEN = "$SMARTHOME_TOKEN";
const TOKEN_FOR_SMART_HOME = "$SMARTHOME_TOKEN";
const SMART_HOME_HOST = "https://$SMARTHOME_DOMAIN";
const SMART_HOME_HOST2 = "https://$SMARTHOME_DOMAIN";
const CSGAET_NET_FOR_SMARTHOME = "${GATE_HTTPS_DOMAIN}";

#csliner服务器内网ip
const CSLINKER_HTTP_SERVER = '${CSLINKER_HTTP_SERVER}';

#csfacecut
const CSFACECUT_ADDR = '${CSFACECUT_ADDR}';

//kafka集群服务器地址 多个以,分隔
const KAFKA_BROKER_LIST = '${KAFKA_INNER_IP}';

const MQTT_DOMAIN = '${mqtt_domain}';
const MQTT_TLS_PORT = '${mqtt_port}';
const WEB_ADAPT_ENTRY = '${WEB_ADAPT_ENTRY}';
const FILE_SERVER = '${FILE_SERVER}';
const SMARTLOCK_HTTP_GATE_SERVER = 'https://${SMARTLOCK_HTTP_GATE_SERVER}';

EOF
}


PKG_ROOT=$RSYNC_PATH

IP_FILE=/etc/ip
INSTALL_CONF=$PKG_ROOT/web_backend_install.conf
KDC_CONF=/etc/kdc.conf
APP_BACKEND_CONF=$PKG_ROOT/app_backend_install.conf
SVR_CONF=/etc/app_backend_install.conf
HTML_ROOT=/var/www/html
PHP_CONFIG=dynamic_config.php

# 检查 KDC_CONF 文件是否存在
if [ ! -f "$KDC_CONF" ]; then
    echo "文件不存在： $KDC_CONF 不存在."
    exit 1
fi

# ----------------------------------------------------------------------------
# 开始安装
# ----------------------------------------------------------------------------
echo "Begin to install smartHome2"


echo '读取配置'

if [ ! -f $IP_FILE ]; then
    echo "文件不存在：$IP_FILE"
    exit 1
fi

if [ ! -f $INSTALL_CONF ]; then
    echo "文件不存在：$INSTALL_CONF"
    exit 1
fi

# 读取配置
GATE_IPV6=$(grep_conf 'GATE_IPV6' $INSTALL_CONF)
WEB_IP=$(grep_conf 'WEB_IP' $INSTALL_CONF)
WEB_IPV6=$(grep_conf 'WEB_IPV6' $INSTALL_CONF)
SYSTEM_AREA=$(grep_conf 'SYSTEM_AREA' $INSTALL_CONF)
GATEWAY_NUM=$(grep_conf 'GATEWAY_NUM' $INSTALL_CONF)
EXPAND_GATEWAY_NUM=$(grep_conf 'EXPAND_GATEWAY_NUM' $INSTALL_CONF)
CSVSIP=$(grep_conf 'CSVSIP' $INSTALL_CONF)
CSVSIPV6=$(grep_conf 'CSVSIP' $INSTALL_CONF)
WEB_DOMAIN=$(grep_conf 'WEB_DOMAIN' $INSTALL_CONF)
# FDFS_INNER_IP=$(grep_conf 'FDFS_INNER_IP' $INSTALL_CONF)
GATE_DOMAIN_NAME=$(grep_conf 'GATE_DOMAIN_NAME' $INSTALL_CONF)
# REMOTE_CONIFG_DOMAIN_NAME=$(grep_conf 'REMOTE_CONIFG_DOMAIN_NAME' $INSTALL_CONF)
BM_DOMAIN=$(grep_conf 'BM_DOMAIN' $INSTALL_CONF)
BM_IP=$(grep_conf 'BM_IP' $INSTALL_CONF)
PBX_OUTER_IPV4=$(grep_conf 'PBX_OUTER_IPV4' $INSTALL_CONF)
PBX_OUTER_IPV6=$(grep_conf 'PBX_OUTER_IPV6' $INSTALL_CONF)
REDIS_INNER_IP=$(grep_conf 'REDIS_INNER_IP' $INSTALL_CONF)
ENABLE_REDIS_SENTINEL=$(grep_conf 'ENABLE_REDIS_SENTINEL' $INSTALL_CONF)
SENTINEL_HOSTS=$(grep_conf 'SENTINEL_HOSTS' $INSTALL_CONF)
SMARTHOME_ENABLE=$(grep_conf 'SMARTHOME_ENABLE' $INSTALL_CONF)
SMARTHOME_DOMAIN=$(grep_conf 'SMARTHOME_DOMAIN' $INSTALL_CONF)
SMARTHOME_TOKEN=$(grep_conf 'SMARTHOME_TOKEN' $INSTALL_CONF)
GATE_IP=$(grep_conf 'GATE_IP' $INSTALL_CONF)
FREESWITCH_MYSQL_INNER_IP=$(grep_conf 'FREESWITCH_MYSQL_INNER_IP' $INSTALL_CONF)
FREESWITCH_MYSQL_PORT=$(grep_conf 'FREESWITCH_MYSQL_PORT' $INSTALL_CONF)
GATE_HTTPS_DOMAIN=$(grep_conf 'GATE_HTTPS_DOMAIN' $INSTALL_CONF)
CSLINKER_HTTP_SERVER=$(grep_conf 'CSLINKER_HTTP_SERVER' $APP_BACKEND_CONF)
ETCD_INNER_IP=$(grep_conf 'ETCD_INNER_IP' $SVR_CONF)
SERVER_INNER_IP=$(grep_conf 'SERVER_INNER_IP' $IP_FILE)
ENABLE_AKCS_DBPROXY=$(grep_conf 'ENABLE_DBPROXY' $INSTALL_CONF)
ENABLE_LOG_DBPROXY=$(grep_conf 'ENABLE_LOG_DBPROXY' $INSTALL_CONF)
AKCS_MYSQL_INNER_IP=$(grep_conf 'MYSQL_INNER_IP' $INSTALL_CONF)
LOG_MYSQL_INNER_IP=$(grep_conf 'LOG_MYSQL_INNER_IP' $INSTALL_CONF)
AKCS_DBPROXY_INNER_IP=$(grep_conf 'DBPROXY_INNER_IP' $INSTALL_CONF)
LOG_DBPROXY_INNER_IP=$(grep_conf 'LOG_DBPROXY_INNER_IP' $INSTALL_CONF)
FREESWITCH_DB_CLUSTER1_IP=$(grep_conf 'FREESWITCH_DB_CLUSTER1_IP' $APP_BACKEND_CONF || echo '')
KAFKA_INNER_IP=$(grep_conf 'KAFKA_INNER_IP' $INSTALL_CONF)
FILE_SERVER=$(grep_conf 'FILE_SERVER' $APP_BACKEND_CONF)
SMARTLOCK_HTTP_GATE_SERVER=$(grep_conf 'APP_REST_SSL_ADDR' $APP_BACKEND_CONF)

AKCS_DATABASEIP="$AKCS_MYSQL_INNER_IP";
AKCS_DATABASEPORT=3306;
LOG_DATABASEIP="$LOG_MYSQL_INNER_IP";
LOG_DATABASEPORT=3306;

#获取数据库密码
ENCRYPTED_DB_PASSWORD=$(grep '^AKCS_DBUSER01=' $KDC_CONF | awk '{print substr($0, index($0, "=") + 1)}')

# 检查 /bin/crypto 是否存在
if [ ! -x "/bin/crypto" ]; then
    echo "加密工具 /bin/crypto 不存在或不可执行."
    exit 1
fi
# 检查 ENCRYPTED_DB_PASSWORD 是否为空
if [ -z "$ENCRYPTED_DB_PASSWORD" ]; then
    echo "错误: 获取到的加密数据库密码为空."
    exit 1
fi
# 解密数据库密码
DECRYPTED_DB_PASSWORD=$(echo "$ENCRYPTED_DB_PASSWORD" | /bin/crypto -d 2>/dev/null)
# 检查解密是否成功
if [ -z "$DECRYPTED_DB_PASSWORD" ]; then
    echo "错误：无法解密数据库密码！"
    exit 1
fi


if [ $ENABLE_AKCS_DBPROXY -eq 1 ];then
    AKCS_DATABASEIP="$AKCS_DBPROXY_INNER_IP";
    AKCS_DATABASEPORT=3308;
fi

if [ $ENABLE_LOG_DBPROXY -eq 1 ];then
    LOG_DATABASEIP="$LOG_DBPROXY_INNER_IP";
    LOG_DATABASEPORT=3308;
fi

if [ "$SMARTHOME_ENABLE" -eq 1 ]; then
    SMARTHOME_ENABLE=true
else
    SMARTHOME_ENABLE=false
fi

etcd_ret=`/usr/local/bin/php /bin/etcd_cli.php /bin/etcdctl "$ETCD_INNER_IP" get /akcs/csfacecut/innerip --prefix`
for etcd_ret_value in $etcd_ret; do
    CSFACECUT_ADDR="${etcd_ret_value}"
done

export ETCDCTL_API=3
etcd_ret=`/bin/etcdctl --endpoints=$ETCD_INNER_IP get /akconf/mqtt/outer_addr/tls --prefix`
etcd_ret=`echo $etcd_ret | awk '{print $2}' | tr -d '\n'`
domain_port=$(echo "$etcd_ret" | sed 's|^ssl://||')
mqtt_domain=$(echo "$domain_port" | cut -d: -f1)
mqtt_port=$(echo "$domain_port" | cut -d: -f2)

WEB_ADAPT_ENTRY=`/bin/etcdctl --endpoints=$ETCD_INNER_IP get akcs/web/adaptEntry/appBackend --prefix`
WEB_ADAPT_ENTRY=`echo $WEB_ADAPT_ENTRY | awk '{print $2}' | tr -d '\n'`

if [[ "$CSFACECUT_ADDR" = "" || "$mqtt_domain" = "" || "$mqtt_port" = "" || "$WEB_ADAPT_ENTRY" = "" ]]
then
    echo "Error: etcd server check failed. Please check etcd server is ok."
    echo "Variable values:"
    echo "CSFACECUT_ADDR: '$CSFACECUT_ADDR'"
    echo "mqtt_domain: '$mqtt_domain'"
    echo "mqtt_port: '$mqtt_port'"
    echo "WEB_ADAPT_ENTRY: '$WEB_ADAPT_ENTRY'"
    exit 1
fi


PACKAGE_SLIM_PATH=$PKG_ROOT/slim
PACKAGE_SMARTHOME_PBX_API_PATH=$PKG_ROOT/SmartHomePbxApi
PACKAGE_SLIM_WEBMAN_PATH=$PKG_ROOT/slim_webman

RUN_SLIM_PATH=/var/www/html/slim
RUN_SMARTHOME_PBX_API_PATH=/var/www_for_smarthome/html/SmartHomePbxApi
RUN_SLIM_WEBMAN_PATH=/var/www/html/slim_webman

DOCKER_APP_NAME=slim_webman


# 开始安装
if [ -z "$MIDDLEWARE" ];then
    echo "请选择需要部署的中间件";
    exit 1
fi

array=(${MIDDLEWARE//,/ })  
for var in ${array[@]}
do
    if [ "$var" == "slim" ];then
        # 生成配置文件 dynamic_config.php
        if [ ! -d $PACKAGE_SLIM_PATH ]; then mkdir -p $PACKAGE_SLIM_PATH; fi

        create_php_config "$PACKAGE_SLIM_PATH"/dynamic_config.php
        mv -f "$PACKAGE_SLIM_PATH"/dynamic_config.php "$PACKAGE_SLIM_PATH"/framework/config/

        #将slim同步到运行路径
        rsync -av --delete \
        --exclude="shell" \
        --exclude="app_backend_install.conf" \
        --exclude="web_backend_install.conf" \
        --exclude="web_frontend_install.conf" $PACKAGE_SLIM_PATH/* $RUN_SLIM_PATH/
    elif [ "$var" == "smarthome_pbx_api" ];then
        if [ ! -d $PACKAGE_SMARTHOME_PBX_API_PATH ]; then mkdir -p $PACKAGE_SMARTHOME_PBX_API_PATH; fi

        mkdir -p $RUN_SMARTHOME_PBX_API_PATH
        chmod 755 $RUN_SMARTHOME_PBX_API_PATH

        #pbxapi
        sed -i "
            s/^.*const FS_DB_IP.*/const FS_DB_IP=\"$FREESWITCH_MYSQL_INNER_IP\";/g
            s/^.*const FS_DB_CLUSTER1_IP.*/const FS_DB_CLUSTER1_IP=\"$FREESWITCH_DB_CLUSTER1_IP\";/g
            s/^.*const FS_DB_PORT.*/const FS_DB_PORT=\"$FREESWITCH_MYSQL_PORT\";/g
            s/^.*const DATABASEIP.*/const DATABASEIP=\"$AKCS_MYSQL_INNER_IP\";/g
            s/^.*const DATABASEPWD.*/const DATABASEPWD=\"$DECRYPTED_DB_PASSWORD\";/g" "$PACKAGE_SMARTHOME_PBX_API_PATH"/common/define.php

        rsync -av --delete \
        --exclude="shell" \
        --exclude="app_backend_install.conf" \
        --exclude="web_backend_install.conf" \
        --exclude="web_frontend_install.conf" $PACKAGE_SMARTHOME_PBX_API_PATH/* $RUN_SMARTHOME_PBX_API_PATH/
		
		chmod 777 /var/www_for_smarthome/html/SmartHomePbxApi/scripts/etcdctl
        ret=`/usr/local/bin/php /var/www_for_smarthome/html/SmartHomePbxApi/scripts/etcd_cli.php /var/www_for_smarthome/html/SmartHomePbxApi/scripts/etcdctl "$ETCD_INNER_IP" put /akconf/smg/serverConfig/cloud-pbx "'{\"host\":\"https://'$SERVER_INNER_IP':8531\",\"kafkaConfig\":{\"topic\":\"\"}}'"`
    
    elif [ "$var" == "slim_webman" ];then
        # 生成配置文件 dynamic_config.php
        if [ ! -d $PACKAGE_SLIM_WEBMAN_PATH ]; then mkdir -p $PACKAGE_SLIM_WEBMAN_PATH; fi

        create_php_config "$PACKAGE_SLIM_WEBMAN_PATH"/dynamic_config.php
        mv -f "$PACKAGE_SLIM_WEBMAN_PATH"/dynamic_config.php "$PACKAGE_SLIM_WEBMAN_PATH"/app/config/

        #将slim同步到运行路径
        rsync -av --delete \
        --exclude="shell" \
        --exclude="app_backend_install.conf" \
        --exclude="web_backend_install.conf" \
        --exclude="web_frontend_install.conf" $PACKAGE_SLIM_WEBMAN_PATH/* $RUN_SLIM_WEBMAN_PATH/
        
        if [ `docker ps -a | grep $DOCKER_APP_NAME | wc -l` -gt 0 ];then docker stop $DOCKER_APP_NAME;docker rm $DOCKER_APP_NAME;fi
        #拉起容器

        docker run -itd -e TZ=Asia/Shanghai --restart=always -p 8079:8787 -p 9404:9404 -v $RUN_SLIM_WEBMAN_PATH:$RUN_SLIM_WEBMAN_PATH -v /var/log/php:/var/log/php -v /var/adapt_sock:/var/adapt_sock -v /usr/local/php/etc/client.conf:/usr/local/out/php/etc/client.conf  -v /etc/oss_install.conf:/etc/oss_install.conf --name $DOCKER_APP_NAME registry.cn-hangzhou.aliyuncs.com/ak_system/web php $RUN_SLIM_WEBMAN_PATH/start.php start        
        #监控插件安装
        echo "===执行监控安装==="
        bash -x $RSYNC_PATH/shell/monitor.sh $RSYNC_PATH $PROJECT_RUN_PATH $ENV $HOST $HOSTNAME

        #注册etcd
        export ETCDCTL_API=3
        SLIM_WEBMAN_ADDR="$SERVER_INNER_IP:8079"
        ret=$(/usr/local/bin/php /bin/etcd_cli.php /bin/etcdctl "$ETCD_INNER_IP" put akcs/slim_webman/inner/${SERVER_INNER_IP} "$SLIM_WEBMAN_ADDR")
        if [[ $ret != *ok!* ]]; then
            echo "plz check etcd server is ok."
            exit 1
        fi
    fi
done


# php 日志
mkdir -p /var/log/php
touch /var/log/php/php-error.log
touch /var/log/php/smarthomepbx.log
chown nobody:nogroup /var/log/php/smarthomepbx.log
touch /var/log/php/restful.log
chown nobody:nogroup /var/log/php/restful.log
touch /var/log/php/slim-request.log
chown nobody:nogroup /var/log/php/slim-request.log

echo "install complete."

