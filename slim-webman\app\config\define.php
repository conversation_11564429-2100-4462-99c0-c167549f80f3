<?php

ini_set('date.timezone', 'Asia/Shanghai');
require_once(dirname(__FILE__).'/dynamic_config.php');
require_once(dirname(__FILE__).'/confusionFields.php');

const AREA_NODE_TYPE_NONE = 0;
const AREA_NODE_TYPE_AREA = 1;
const AREA_NODE_TYPE_BUILDING = 2;
const AREA_NODE_TYPE_UNIT = 3;
const AREA_NODE_TYPE_FLOOR = 4;
const AREA_NODE_TYPE_ROOM = 5;
const AREA_NODE_TYPE_MAX = 6;
//设备类型
const DEVICE_TYPE_STAIR = 0;
const DEVICE_TYPE_DOOR = 1;
const DEVICE_TYPE_INDOOR = 2;
const DEVICE_TYPE_MANAGEMENT = 3;
const DEVICE_TYPE_WALL = 4;
const DEVICE_TYPE_ACCESS = 50;

const COMMUNITY_DEVICE_TYPE_NONE = 0;//代表个人
const COMMUNITY_DEVICE_TYPE_PUBLIC = 1; //公共设备
const COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT = 2; //单元公共设备
const COMMUNITY_DEVICE_TYPE_PERSONAL = 3; //个人设备

const REDISSOCKET = 8504;//redis 端口
const REDISPW = "Akcs#xm2610*";//redis 密码

const HTTP_HEAD_API_SCENE = "api-scene";
const CSSMARTLOCK_USERNAME = 'akcs';
const CSSMARTLOCK_PASSWORD = 'mqttAk20#24!ypt';

//用户表switch
const PersonlAccountSwitchPinInit = 0;
const PersonlAccountSwitchFeature = 1;  //单住户高级功能开关
const PersonlAccountSwitchConfirm = 2;  //高级设置开关
//用户Cnf表Flags
const PersonlAccountCnfFlagsFamilyMember = 0;
const PersonlAccountCnfFlagsFace = 1;
const PersonlAccountCnfFlagsIDAccess = 2;
const PersonlAccountCnfFlagsRfCard = 3;
//设备表switch
const DevSwitchEnableLandline = 0;
const DevSwitchEnablePinConfig = 2;
const DevSwitchIndoorOnLine = 8;
//社区表switch
const CommunitySwitchEnableSmartHome = 4;

//社区方案，标识创建者类型
const CommunityPlanCreatorTypeAK = 1; //常规网页创建方式
const CommunityPlanCreatorTypeInstallerKit = 2; //即插即用Installer Kit创建方式
//feature plan item
const FeatureItemPin = 1;
const FeatureItemTempkey = 2;
const FeatureItemFamilyControl = 3;
const FeatureItemFaceRec = 5;
const FeatureItemThirdCamera = 6;
const FeatureItemIDAccess = 7;
const FeatureItemBooking = 8;
const FeatureItemCreateRFCard = 9;
//设备Flags
const DevFlagsManagement = 3;
const DevFlagsRelay1 = 4;
const DevFlagsRelay2 = 5;
const DevFlagsRelay3 = 6;
const DevFlagsRelay4 = 7;
const DevFlagsSecurityRelay1 = 10;
const DevFlagsSecurityRelay2 = 11;
const DevFlagsSecurityRelay3 = 12;
const DevFlagsSecurityRelay4 = 13;
//设备function
const DevFunctionSmartHomeType = 4; //家居类型设备
const DevFunctionSupportMultiMonitor = 12; //支持双摄像头
const DevFunctionSupportOpenDoorAck = 14; //是否支持开门校验以及结果返回
//PersonalAccountOfficeInfo Flags
const CALL_ENABLE_FLAG = 0;

//Account Flags
const ACCOUNT_FLAGS_COMMUNITY_CONTACT = 4;

//人脸管理
const APP_PERSONAL_UPLOAD_FACE_PIC = 1013; //APP Rest上传人脸，Personal
const APP_PERSONAL_DEL_FACE_PIC = 1014; //APP Rest删除人脸,Personal
const APP_COMM_UPLOAD_FACE_PIC = 2103; //APP Rest上传人脸，Community
const WEB_COMM_DEL_FACE_PIC = 2104; //网页删除人脸
const WEB_COMM_IMPORT_FACE_PIC = 2105; //网页导入人脸
const WEB_COMM_UPDATE_APT_CALLRULE = 2108;//更新Apt相关呼叫规则
const WEB_COMM_UPDATE_RF=2007;//更新nfc/ble/rf
const WEB_PER_UPDATE_RF=1007;//更新nfc/ble/rf
const WEB_COMM_NODE_UPDATE=2000;//更新联动 calltype
const WEB_PER_NODE_UPDATE=1000;//更新联动 calltype
const WEB_COMM_ADD_PM_APP_ACCOUNT = 6507;
const WEB_COMM_DEL_PM_APP_ACCOUNT = 6508;
const WEB_COMM_MODIFY_PM_APP_ACCOUNT = 6509;

//app修改motion
const APP_COMM_MODIFY_NODE_MOTION_CONFIG = 5021;

// app修改Detection(包括motion)
const APP_PER_MODIFY_MOTION_CONFIG = 1017;
const APP_PM_MODIFY_COMM_DETECTION_CONFIG = 5003;
const APP_COMM_MODIFY_NODE_DETECTION_CONFIG = 5027;

// app修改自动关闭时间
const APP_PER_UPDATE_NODE_CONFIG = 1020;
const APP_COMM_MODIFY_NODE_CONFIG = 5028;

const APP_ACCOUNT_HAS_EXPIRED = 1;
const APP_ACCOUNT_NOT_EXPIRED = 0;

const ROLE_TYPE_PERSONNAL_MASTER = '10'; //单住户主账号
const ROLE_TYPE_PERSONNAL_SLAVE = '11'; //单住户家庭成员
const ROLE_TYPE_COMMUNITY_MASTER = '20'; //社区主账号
const ROLE_TYPE_COMMUNITY_SLAVE = '21'; //社区家庭成员
const ROLE_TYPE_OFFICE_EMPLOYEE = '30'; //普通人员
const ROLE_TYPE_OFFICE_ADMIN = '31'; //管理员
const ROLE_TYPE_OFFICE_NEW_PER = '32'; //新办公角色
const ROLE_TYPE_COMMUNITY_PM = '40'; //社区PM账号
const ROLE_TYPE_OFFICE_NEW_ADMIN = '41'; //新办公Admin账号

const USER_TYPE_SIG = 0;
const USER_TYPE_RESIDENCE = 1;
const USER_TYPE_OFFICE = 2;
const USER_TYPE_COMMUNITY_PM = 3;

const GRADE_TYPE_DIS = '11';
const GRADE_TYPE_SUB_DIS = '12';
const GRADE_TYPE_COMM = '21';
const GRADE_TYPE_INSTALLER = '22';
const GRADE_TYPE_OFFICE = '23';
const GRADE_TYPE_PM = '31';

const PROJECT_TYPE_RESIDENCE = 0;
const PROJECT_TYPE_OFFICE = 1;
const PROJECT_TYPE_NEW_OFFICE = 3;
const PROJECT_TYPE_SMRTLOCK = 4;
const PROJECT_TYPE_COMMON = -1;

const SALTO_LOCK_PROJECT_TYPE_PERSONNAL = 1;
const SALTO_LOCK_PROJECT_TYPE_COMMUNITY = 2;
const SALTO_LOCK_PROJECT_TYPE_OFFICE = 3;
const DORMAKABA_LOCK_PROJECT_TYPE_PERSONNAL = 1;
const DORMAKABA_LOCK_PROJECT_TYPE_COMMUNITY = 2;
const LOCK_PROJECT_TYPE_PERSONNAL = 1;
const LOCK_PROJECT_TYPE_COMMUNITY = 2;

const ACCESS_GROUP_CHECK_RELAY = 0;
const ACCESS_GROUP_CHECK_SECURITY_RELAY = 1;

const TABLE_PERSONAL_ACCOUNT_USER_INFO = 0;
const TABLE_ACCOUNT_USER_INFO = 1;

const MESSAGE_RECEIVER_CLIENT_TYPE_APP = 2;

const UPDATE_AUCLOUD_AUTH_URL = "http://3.104.98.95:9999/update_auth";

const UPDATE_SCLOUD_AUTH_URL = "http://47.74.208.82:9999/update_auth";
const UPDATE_UCLOUD_AUTH_URL = "http://52.52.9.50:9999/update_auth";
const UPDATE_ECLOUD_AUTH_URL = "http://18.196.56.189:9999/update_auth";

const LOG_FILE = "/var/log/php/restful.log";
const REQ_LOG_FILE = "/var/log/php/slim-request.log";
const ROOT = 'root';
function LOG_TRACE($content)
{
    $tmpNow = time();
    $Now = date('Y-m-d H:i:s', $tmpNow);
    //当文件不存在时候，返回错误信息 会影响ios解析数据
    @file_put_contents(LOG_FILE, $Now." ".$content, FILE_APPEND);
    @file_put_contents(LOG_FILE, "\n", FILE_APPEND);
}

const FEEDBACK_SAVE_ROOT_DIR = "/var/www/upload/feedback";
//反馈接收主送邮箱
const FEEDBACK_SEND_EMAIL_MAIN = "<EMAIL>"; 
//反馈接收抄送邮箱
const FEEDBACK_SNED_EMAIL_CC_LIST = ["<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>"];

const AKCS_MONITOR_ALARM_SET_DND_FAILED = "alarm.slim.redis.set_dnd";
const AKCS_MONITOR_ALARM_FDFS_UPLOAD_FACE_FAILED = "alarm.fdfs.face.upload_failed";
const REDISDB2TOKEN = 1;//存储token的redis数据库
const TOKENVALIDITYTIME = 3600;
const NO_MONITOR_MODEL = array(101,102,103,92,94,33);

const LOGIN_INTERCEPT_WORD_KEY1  = 'textUpgradeMsg1';   // 请升级APP版本再登录
const LOGIN_INTERCEPT_WORD_KEY2  = 'textUpgradeMsg2';   // 登录失败
const LOGIN_INTERCEPT_WORD_KEY3  = 'loginInvalidTime';  // 账号不在登录时间内
const LOGIN_INTERCEPT_WORD_KEY4  = 'textUpgradeMsg3';   // 升级版本

const QRIO_LOCK_TYPE = 0;
const YALE_LOCK_TYPE = 1;
const BSI_LOCK_TYPE = 2;
const DORMAKABA_LOCK_TYPE = 3;
const SL20_LOCK_TYPE = 4;
const SALTO_LOCK_TYPE = 5;
const ITEC_LOCK_TYPE = 6;
const TT_LOCK_TYPE = 7;
const ITEC_LOCK_GRADE_APT = 3;

const NOTIFY_SMARTLOCK_TYPE_SL20 = 0;
const NOTIFY_SMARTLOCK_TYPE_SL50 = 1;

const LINKER_MSG_TYPE_TRIGGER = 0;
const LINKER_MSG_TYPE_KIT = 1;
const LINKER_MSG_TYPE_QRIO_OPEN_DOOR = 2;
const LINKER_MSG_TYPE_QRIO_CLOSE_DOOR = 3;
const LINKER_MSG_TYPE_YALE_OPEN_DOOR = 4;
const LINKER_MSG_TYPE_YALE_CLOSE_DOOR = 5;
const LINKER_MSG_TYPE_OPENDOOR = 6;
const LINKER_MSG_TYPE_MESSAGE = 7;
const LINKER_MSG_TYPE_TMPKEY = 8;
const LINKER_MSG_TYPE_DELIVERY = 9;
const LINKER_MSG_TYPE_ACCOUNT_EXPIRE = 10;
const LINKER_MSG_TYPE_WEATHER = 11;
const LINKER_MSG_TYPE_PACPORT_REGIST = 12;
const LINKER_MSG_TYPE_PACPORT_UNREGIST = 13;
const LINKER_MSG_TYPE_PACPORT_UNLOCK_CHECK = 14;
const LINKER_MSG_TYPE_KIT_CREATE_ROOM = 15;
const LINKER_MSG_TYPE_KIT_DELETE_ROOM = 16;
const LINKER_MSG_TYPE_KIT_RESET_ROOM = 17;
const LINKER_MSG_TYPE_KIT_DELETE_MAC = 18;
const LINKER_MSG_TYPE_KIT_REPLACE_MAC = 19;
const LINKER_MSG_TYPE_DORMAKABA_OPEN_DOOR = 20;
const LINKER_MSG_TYPE_SALTO_OPEN_DOOR = 21;
const LINKER_MSG_TYPE_ITEC_OPEN_DOOR = 22;
const LINKER_MSG_TYPE_TT_OPEN_DOOR = 23;

const THIRD_PARTY_LOCK_CAPTURE_TYPE_CALL = 0;
const THIRD_PARTY_LOCK_CAPTURE_TYPE_TMPKEY = 1;
const THIRD_PARTY_LOCK_CAPTURE_TYPE_LOCALKEY = 2;
const THIRD_PARTY_LOCK_CAPTURE_TYPE_RFCARD = 3;
const THIRD_PARTY_LOCK_CAPTURE_TYPE_FACE = 4;
const THIRD_PARTY_LOCK_CAPTURE_TYPE_REMOTE_OPEN_DOOR = 5;
const THIRD_PARTY_LOCK_CAPTURE_TYPE_NFC = 100;
const THIRD_PARTY_LOCK_CAPTURE_TYPE_BLE = 101;
const THIRD_PARTY_LOCK_CAPTURE_TYPE_AUTO_LOCK = 200;
const THIRD_PARTY_LOCK_CAPTURE_TYPE_LOCK_BY_APP = 201;

const YALE_AUTH_URL = 'https://oauth.aaecosystem.com';
const YALE_API_URL = 'https://api.aaecosystem.com';
const YALE_CLIENT_ID = 'a4513599-4fac-46b3-8fd2-aab74f56af3c';
const YALE_CLIENT_SECRET = 'c3f6bbbff59e108fc1d880fa5e14de48';
const YALE_API_KEY = '218df464-12c1-4174-8b40-6f5b3006d9be';
const YALE_REDIRECT_URL = 'https://'.WEB_DOMAIN.'/smartplus/DealYaleCode.html';
const YALE_L2_HHID_LIST = ["317d4641-7192-4e45-afc9-b167752a5547","a7c38863-8949-4059-a784-2f54aa908b97","b297023f-898a-4866-80c6-75aa4af58a40","5acb26dc-6a24-4edc-b973-a47c98519eaa"];

const QRIO_AUTH_URL = 'https://qrio-production.auth0.com';
const QRIO_API_URL = 'http://sl2-api.qrioinc.com/v1/u';
const QRIO_CLIENT_ID = 'rK0ykDXBMm5Z8RnRn3yNrxN1SwJbj5ie';
const QRIO_CLIENT_SECRET = 'a1qNs4Gx9yKLL9lTP3Qc5-bdn78vdkn3ZdEj-AdeOsQZmR1LRVlO0mFNs2jdN6D9';
const QRIO_REDIRECT_URL = 'https://'.WEB_DOMAIN.'/smartplus/DealQrioCode.html';

const BSI_CODE_TYPE_SECURITY = 0;
const BSI_CODE_TYPE_ONCE = 1;
const BSI_CODE_TYPE_PERMENANT = 2;
const BSI_CODE_TYPE_LIST = [BSI_CODE_TYPE_SECURITY, BSI_CODE_TYPE_ONCE, BSI_CODE_TYPE_PERMENANT];

const BSI_ERROR_CODE_SYSTEM = -1;
const BSI_ERROR_CODE_FORMAT = -2;
const BSI_ERROR_CODE_NUM = -3;
const BSI_ERROR_CODE_EXIST = -4;

const REST_SERVER_URL = 'https://'.WEB_DOMAIN.':8443';
const LOCK_CAPTURE_TYPE_OPEN = 202;
const LOCK_CAPTURE_TYPE_CLOSE = 203;
const LOCK_MESSAGE_TYPE_2WEEK = 4;
const LOCK_MESSAGE_TYPE_1WEEK = 5;
const LOCK_MESSAGE_TYPE_2DAY = 6;

const THIRDPARTY_DOOR_STATUS_OPEN = 0;
const THIRDPARTY_DOOR_STATUS_CLOSE = 1;
const THIRDPARTY_DOOR_STATUS_UNKNOWN = 2;

const FACE_CREATOR_TYPE_PM = 0;
const FACE_CREATOR_TYPE_ENDUSER = 1;

//人脸检测错误码
const UPLOAD_FACEPIC_ERROR_SYSTEM = -1;      //System Error：系统错误，包括解码失败，重命名图片失败等
const UPLOAD_FACEPIC_SUCCESS = 0;
const UPLOAD_FACEPIC_ERROR_NOT_FRONT_VIEW = 100;      //Not front view：人脸的旋转角度 或俯视、仰视、侧脸的角度过大
const UPLOAD_FACEPIC_ERROR_WEAR_MASK = 101;      //Mask detected：检测到口罩
const UPLOAD_FACEPIC_ERROR_LOW_RESOLUTION = 102;      //Resolution is too low.：人脸分辨率太小
const UPLOAD_FACEPIC_ERROR_WRONG_FORMAT = 103;      //File format error.：人脸格式错误
const UPLOAD_FACEPIC_ERROR_NO_FACE= 104;      //No face dectected.：图片中未检测到人脸
const UPLOAD_FACEPIC_ERROR_FILE_LARGE = 105;      //The file is too larger：图片大于10MB
const UPLOAD_FACEPIC_ERROR_FACE_LARGE = 106;      //The face is too larger.：图片中人脸过大
const UPLOAD_FACEPIC_ERROR_FACE_SMALL= 107;      //The face is too small：图片中人脸过小
const UPLOAD_FACEPIC_ERROR_MULTI_FACES= 108;      //More than one face：图片中人脸不止1个
const UPLOAD_FACEPIC_ERROR_WRONG_NAME= 109;      //File name is error.：文件名错误
const UPLOAD_FACEPIC_ERROR_EMPTY_NAME= 110;      //Resident's name is empty.：住户名字为空
const UPLOAD_FACEPIC_ERROR_NO_ACCOUNT_INFO= 111;      //Get PersonalAccount Info error.：无用户信息
const UPLOAD_FACEPIC_ERROR_ACCOUNT_INACTIVE= 112;      //The PersonalAccount is not active.：账户未激活
const UPLOAD_FACEPIC_ERROR_NOT_CLEAR= 113;      //Face not clear enough.: 图片中人脸不清晰

const FILE_FULL_NAME = 'fileFullName';
const FILE_FULL_DETECT_NAME = 'fileFullDetectName';
const FILE_NAME = 'fileName';
const FILE_ENC_NAME = 'fileEncName';
const FACE_FILE_MD5 = 'faceFileMD5';
const FACE_FILE_URL = 'faceUrl';
const FACE_FILE_PREFIX = '/var/www/download/face';
const MAX_DND_MINUTE = 1440;
const FACE_FDFS_GROUP = 'group2';

const PROPERTY_MANAGER = "Property Manager";

// 账号状态
const ERR_SUCCESS = 0;
const ERR_TOKEN_EXPIRE = 1;
const ERR_USER_NOT_EXIT = 2;
const ERR_PASSWD_INVALID = 2;
const ERR_APP_EXPIRE = 3;
const ERR_APP_UNACTIVE = 4;
const ERR_APP_UNPAID = 5; //额外的app未支付
const ERR_TOKEN_INVALID = 7;
const ERR_VERSION_INVALID = 8;
const ERR_PM_APP_STATUS_CLOSED = 9; //PM APP的状态是关闭的

const DEIVCE_DISABLE_REPOST = 0; // 设备开门不需要转发

const SERVER_LOCATION_CCLOUND = "cn";
const SERVER_LOCATION_UCLOUND = "na";
const SERVER_LOCATION_JCLOUND = "jp";

//中国落地
const PBX_LANDLINE_NUMBER_CCLOUND =['05923225070']; 
//日本落地
const PBX_LANDLINE_NUMBER_JCLOUND =['+815018073739'];
//scloud/ecloud/rucloud默认落地
const PBX_LANDLINE_NUMBER_OTHERS =['+15036837202']; 

const RELAY_TYPE_LOCAL = 0;
const RELAY_TYPE_SE_LOCAL = 1;
const RELAY_TYPE_EXTERN = 2;

const RELAY_OUTPUT_TYPE_EXTERNAL = 0;
const RELAY_OUTPUT_TYPE_DIGITAL_OUTPUT = 1;

//美国按洲分配落地
// +18635914605 us_Florida
// +15593445095 us_California        
// +15513158120us_Texas        
// +17379779003 us_New_Jersey 
// +18382026226 us_NewYork
// +15037446210 us_default
// +19027019367 canada
const PBX_LANDLINE_NUMBER_UCLOUND_DEFAULT =['+15037446210']; 
const PBX_LANDLINE_NUMBER_UCLOUND =  array(
    '+18382026226' => array('1212', '1315', '1332', '1347', '1363', '1516', '1518', '1585', '1607', '1646', '1680', '1716', '1718', '1838', '1845', '1914', '1917', '1929', '1934'),
    '+15593445095' => array('1209', '1213', '1279', '1310', '1323', '1341', '1350', '1408', '1415', '1424', '1442', '1510', '1530', '1559', '1562', '1619', '1626', '1628', '1650', '1657', '1661', '1669', '1707', '1714', '1747', '1760', '1805', '1818', '1820', '1831', '1840', '1858', '1909', '1916', '1925', '1949', '1951'),
    '+18635914605' => array('1239', '1305', '1321', '1352', '1386', '1407', '1448', '1561', '1656', '1689', '1727', '1754', '1772', '1786', '1813', '1850', '1863', '1904', '1941', '1954'),
    '+17379779003' => array('1201', '1551', '1609', '1640', '1732', '1848', '1856', '1862', '1908', '1973'),
    '+15513158120' => array('1210', '1214', '1254', '1281', '1325', '1346', '1361', '1409', '1430', '1432', '1469', '1512', '1682', '1713', '1726', '1737', '1806', '1817', '1830', '1832', '1903', '1915', '1936', '1940', '1945', '1956', '1972', '1979'),
    '+19027019367' => array('1781', '1403', '1250', '1604', '1807', '1519', '1204', '1506', '1709', '1867', '1902', '1705', '1613', '1416', '1905', '1514', '1450', '1418', '1819', '1306', '1867'),
);

const DOOR_SUBSCRIPTION_STATUS_NORMAL = 0;
const DOOR_SUBSCRIPTION_STATUS_NOT_ACTIVE = 1;
const DOOR_SUBSCRIPTION_STATUS_EXPIRE = 2;
const RELAY_FULL_ACCESS_VALUE = 127;
const DOOR_RELAY_INDEX_MAPPING = array('A' => 0, "B" => 1 , "C" => 2, "D" => 3);

const REDIS_SENTINEL_TIMEOUT = 0.5;

const APP_SIP_TYPE_UDP = 0;
const APP_SIP_TYPE_TCP = 1;
const APP_SIP_TYPE_TLS = 2;
const APP_SIP_TYPE_NONE = 3;

const RTP_CONFUSE = 1 << 0;

const TRIGGER_EMAIL = "<EMAIL>";
const TRIGGER_EMAIL_CHAIN = 0;
const TRIGGER_THIRDPARTY_CHAIN = 1;
const TRIGGER_FTP_UPLOAD_CHAIN = 2;
const TRIGGER_REFRESH_CONFIG_CHAIN = 3;
const LOG_TABLE_CAPTURE = "PersonalCapture";

const TIANDY_API_URL = "https://opensdk.myviewcloud.com:9000/api/appuser";
const TIANDY_APP_SECRET = "sjGCvDeRG0dhWBGRSej9xakdGxdM8oB9";
const TIANDY_APP_KEY = "10000JLR5NmWCrOhHePG5OUrjWI4lfshlIAMT";
const TIANDY_API_STATUS_CODE_OK = "API-COMMON-INF-OK";
const TIANDY_APP_ALREADY_REGISTRATION = 0;
const TIANDY_APP_FIRST_REGISTRATION = 1;

const PM_MANAGE_PROEJCT_NULL = 0;
const PM_MANAGE_PROJECT_OLD_COMMUNTIY = 1;
const PM_MANAGE_PROJECT_NEW_COMMUNITY = 2;
const PM_MANAGE_PROJECT_OLD_OFFICE = 3;
const PM_MANAGE_PROJECT_NEW_OFFICE = 4;

const ALL_RELAY_ON = 127;

const DOOR_RELAY_TYPE = 0;
const DOOR_SECURITY_RELAY_TYPE = 1;
const RELAY_TYPE_LOCAL_STR = "Local";

// 旧特定错误码 -- 新增时需要和APP约定好
const OLD_SPECIFIC_ERROR_CODE_ARR = array(
    '101' => [
        'result' => 101,
        'message' => "Mobile Number doesn't exist"
    ],
    '1001' => [
        'result' => 1001,
        'message' => "client_id does not exist"
    ],
    '1002' => [
        'result' => 1002,
        'message' => "client_secret error"
    ],
    '1003' => [
        'result' => 1003,
        'message' => "authorization_code error"
    ],
    '1004' => [
        'result' => 1004,
        'message' => "refresh_token error"
    ],
    '1005' => [
        'result' => 1005,
        'message' => "token is expired"
    ],
    '1006' => [
        'result' => 1006,
        'message' => "access_token error"
    ],
    '1007' => [
        'result' => 1007,
        'message' => "Permission is not allowed"
    ],
    '1008' => [
        'result' => 1008,
        'message' => "You haven't set the key yet"
    ],
    '1009' => [
        'result' => 1009,
        'message' => "Account does not exist"
    ],
    '1010' => [
        'result' => 1010,
        'message' => "Password error"
    ],
    '1011' => [
        'result' => 1011,
        'message' => "code is expired"
    ],
    '1012' => [
        'result' => 1012,
        'message' => "Your account is not activated"
    ],
    '1013' => [
        'result' => 1013,
        'message' => "Your param is invalid"
    ],
    '1014' => [
        'result' => 1014,
        'message' => "Your startTime endTime is invalid"
    ],
    '1015' => [
        'result' => 1015,
        'message' => "You have no authority to open a door"
    ],
    '1016' => [
        'result' => 1016,
        'message' => 'BindCode has been binded or not exist.'
    ],
    '1017' => [
        'result' => 1017,
        'message' => 'BindCode have been binded already or app conf file is not exist.'
    ],
    '1018' => [
        'result' => 1018,
        'message' => 'we don not find request header；api-version'
    ],
    '1019' => [
        'result' => 1019,
        'message' => 'the api-version is wrong'
    ],
    '1020' => [
        'result' => 1020,
        'message' => 'the token does not exist'
    ],
    '1021' => [
        'result' => 1021,
        'message' => 'Please upload face image'
    ],
    '1022' => [
        'result' => 1022,
        'message' => 'Encrypt face image failed'
    ],
    '2001' => [
        'result' => 2001,
        'message' => 'Qrio token is expired'
    ],
    '2002' => [
        'result' => 2002,
        'message' => 'Yale token is expired'
    ],
);

// 新特定错误码
const ERR_CODE_SUCCESS                  = "0";
const ERR_CODE_FAILED                   = "-1";             // 通用错误码
const ERR_CODE_TOKEN_INVALID            = "**********";
const ERR_CODE_TOKEN_EXPIRE             = "**********";
const ERR_CODE_NO_PERMISSION            = "**********";
const ERR_CODE_APP_ACTIVE_BUT_EXPIRED   = "**********";
const ERR_CODE_MASTER_ACCOUNT_UNACTIVE  = "**********";
const ERR_CODE_SLAVE_ACCOUNT_UNACTIVE   = "**********";
const ERR_CODE_PM_APP_CLOSED            = "**********";
const ERR_CODE_INTERCEPT_UPGRADE        = "**********";     // APP识别该错误号后，提示更新并退出APP
const ERR_CODE_INTERCEPT_OK             = "**********";     // APP识别该错误号后，提示更新不退出APP
const ERR_CODE_INVALID_TIME             = "**********";
const ERR_CODE_SALTO_CODE_EXPIRED       = "**********";     // ins账号的Salto 账号token过期（和APP约定好的错误号）
const ERR_CODE_SL20_NOT_EXIST           = "**********";
const ERR_CODE_OPEN_DOOR_FAILED         = "**********";
const ERR_CODE_TWO_FACTOR_AUTH_TEMP_TOKEN_INVALID = "**********";
const ERR_CODE_LOGIN_ACCOUNT_NOT_EXIST = "**********";
const ERR_CODE_SMS_RATE_LIMIT           = "**********";
const ERR_CODE_TT_LOCK_NOT_EXIST        = "**********";
const ERR_CODE_ITEC_LOCK_NOT_EXIST      = "**********";
const ERR_CODE_PHONE_NOT_SUPPORT_ADMIN  = "**********";
const ERR_CODE_DORMAKABA_LOCK_NOT_EXIST      = "**********";
const ERR_CODE_PARAM_INVALID      = "**********";
const ERR_CODE_SET_INDOOR_RELAY_AUTO_CLOSE_TIME_FAILED      = "**********";
const ERR_CODE_SET_INDOOR_RELAY_AUTO_CLOSE_TIME_LIMITED      = "**********";

// 新特定错误码 -- 新增时需要和APP约定好
const NEW_SPECIFIC_ERROR_CODE_ARR = array(
    ERR_CODE_SUCCESS => [
        'err_code' => ERR_CODE_SUCCESS,
        'message' => "success",
        'result' => 0,
    ],
    ERR_CODE_FAILED => [
        'err_code' => ERR_CODE_FAILED,
        'message' => "failed",
        'result' => -1,
    ],
    ERR_CODE_TOKEN_INVALID => [
        'err_code' => ERR_CODE_TOKEN_INVALID,
        'message' => 'token invalid',
        'result' => -1,
    ],
    ERR_CODE_TOKEN_EXPIRE => [
        'err_code' => ERR_CODE_TOKEN_EXPIRE,
        'message' => 'token expired',
        'result' => -1,
    ],
    ERR_CODE_NO_PERMISSION => [
        'err_code' => ERR_CODE_NO_PERMISSION,
        'message' => 'Permission is not allowed',
        'result' => 1,
    ],
    ERR_CODE_APP_ACTIVE_BUT_EXPIRED => [
        'err_code' => ERR_CODE_APP_ACTIVE_BUT_EXPIRED,
        'message' => 'app active but expired',
        'result' => 0,
    ],
    ERR_CODE_MASTER_ACCOUNT_UNACTIVE => [
        'err_code' => ERR_CODE_MASTER_ACCOUNT_UNACTIVE,
        'message' => 'app master account unactive',
        'result' => 0,
    ],
    ERR_CODE_SLAVE_ACCOUNT_UNACTIVE => [
        'err_code' => ERR_CODE_SLAVE_ACCOUNT_UNACTIVE,
        'message' => 'app slave account unactive',
        'result' => 0,
    ],
    ERR_CODE_PM_APP_CLOSED => [
        'err_code' => ERR_CODE_PM_APP_CLOSED,
        'message' => 'pm app status is close',
        'result' => 0,
    ],
    ERR_CODE_INTERCEPT_UPGRADE => [
        'err_code' => ERR_CODE_INTERCEPT_UPGRADE,
        'message' => 'intercept window upgrade',
        'result' => 0,
    ],
    ERR_CODE_INTERCEPT_OK => [
        'err_code' => ERR_CODE_INTERCEPT_OK,
        'message' => 'intercept window ok',
        'result' => 0,
    ],
    ERR_CODE_INVALID_TIME => [
        'err_code' => ERR_CODE_INVALID_TIME,
        'message' => 'Your account is invalid right now',
        'result' => 0,
    ],
    ERR_CODE_SALTO_CODE_EXPIRED => [
        'err_code' => ERR_CODE_SALTO_CODE_EXPIRED,
        'message' => 'salto token is expired',
        'result' => 0,
    ],
    ERR_CODE_SL20_NOT_EXIST => [
        'err_code' => ERR_CODE_SL20_NOT_EXIST,
        'message' => 'SL20lock does not exist',
        'result' => 0,
    ],
    ERR_CODE_OPEN_DOOR_FAILED => [
        'err_code' => ERR_CODE_OPEN_DOOR_FAILED,
        'message' => 'Open door failed',
        'result' => 0,
    ],
    ERR_CODE_SMS_RATE_LIMIT => [
        'err_code' => ERR_CODE_SMS_RATE_LIMIT,
        'message' => 'You have reached the maximum limit of 5 verification codes for today. Please try again tomorrow.',
        'result' => -1,
    ],
    ERR_CODE_PARAM_INVALID => [
        'err_code' => ERR_CODE_PARAM_INVALID,
        'message' => 'Invalid parameter',
        'result' => -1,
    ],
    ERR_CODE_SET_INDOOR_RELAY_AUTO_CLOSE_TIME_FAILED => [
        'err_code' => ERR_CODE_SET_INDOOR_RELAY_AUTO_CLOSE_TIME_FAILED,
        'message' => 'Failed to set smart control device auto close time',
        'result' => -1,
    ],
    ERR_CODE_SET_INDOOR_RELAY_AUTO_CLOSE_TIME_LIMITED => [
        'err_code' => ERR_CODE_SET_INDOOR_RELAY_AUTO_CLOSE_TIME_LIMITED,
        'message' => 'Limit the time for setting up the smart control device ',
        'result' => -1,
    ],
    ERR_CODE_SMARTLOCK_NOT_EXIST => [
        'err_code' => ERR_CODE_SMARTLOCK_NOT_EXIST,
        'message' => 'Smartlock does not exist',
        'result' => -1,
    ],
);

//code和旧app status字段映射关系，默认为0，后续无需新增
const CODE_APPSTATUS_MAP = [
    '**********' => 3,
    '**********' => 4,
    '**********' => 5,
    '**********' => 9,
];

//code和旧intercept mode字段映射关系，默认为0，后续无需新增
const CODE_INTERCEPT_MODE_MAP = [
    '**********' => 1,
    '**********' => 2,
];

//沿用web的state
const STATE_SUCCESS = 0;
const STATE_PHONE_CODE_ERROR = 1;
const STATE_TOKEN_INVAILD = 2;
const STATE_NOT_PERMISSION = 1007;

//third linker state
const STATE_PARAMS_FOR_REST = 110000; //rest接口的错误码开头,用户对接第三方设备,web和h5调用的接口

//用于凯撒的偏移列表,找到字符然后加上偏移得到最终的字符，这个要和C++的对应
const CONFUSE_OFFSET_STR = 'CADaHgKxWec5f2otYIiRlmNLqP0z3hU7ZnFbES8QTOs9dG6yMvu4Jp1VjkrBwX';
const CONFUSE_RANDOM_HEAD_LEN = 4;
const CONFUSE_PASSWORD_INDEX = 5;
const CONFUSE_PASSWORD_LEN = 2;
const CONFUSE_SETP_LEN = 1;//凯撒的偏移setp 用1 位表示
const CONFUSE_OUT_LEN = 32;
const CONFUSE_PREFIX_INDEX = 7;
const CONFUSE_PASSWD_MAX_LEN = 25;//32-7
const CONFUSE_PASSWD_MIN_LEN = 6;
const CONFUSE_OFFSET_STR_MAX_INDEX = 61;
const CONFUSE_OFFSET_STR_LEN = 62;

//audio log相关
const AUDIT_TYPE_LOGIN_IN = 1;
const AUDIT_TYPE_LOGIN_OUT = 2;
const AUDIT_TYPE_IMPORT_FACE = 26;
const AUDIT_TYPE_DELETE_FACE = 27;
const AUDIT_TYPE_SET_CALL_TYPE_SMARTPLUS_INDOOR = 54;
const AUDIT_TYPE_SET_CALL_TYPE_PHONE_INDOOR = 55;
const AUDIT_TYPE_SET_CALL_TYPE_SMARTPLUS_INDOOR_BACKUP = 56;
const AUDIT_TYPE_SET_CALL_TYPE_SMARTPLUS_BACKUP = 57;
const AUDIT_TYPE_SET_CALL_TYPE_INDOOR_PHONE_BACKUP = 58;
const AUDIT_TYPE_SET_CALL_TYPE_INDOOR_SMARTPLUS_PHONE = 59;

//对象存储类型
const STORAGE_TYPE_OSS = 0; //对象存储类型：阿里云
const STORAGE_TYPE_AWS = 1; //对象存储类型：亚马逊云
const STORAGE_TYPE_UKD = 2; //对象存储类型：优刻得

const COMM_CALL_RULE_GROUPCALL = 0;
const COMM_CALL_RULE_SEQCALL = 1;

const THIRD_LOCK_CONNECTED_OFFLINE = 0;
const THIRD_LOCK_CONNECTED_ONLINE = 1;


const REDIS_DB_SMART_LOCK = 30;
const REDIS_DB_TWO_FACTOR_AUTH_CODE = 31;
const REDIS_DB_SMS_RATE_LIMIT = 24;
const SMART_LOCK_OPEN_EXPIRE_TIME = 60;
const SECONDS_IN_A_DAY = 86400;
const TWO_FACTOR_AUTH_CODE_EXPIRE_TIME = 300;

//短信限流
const SMS_RATE_LIMIT_EXPIRE_TIME = 86400;//一天
const SMS_RATE_LIMIT_NUM = 5;

//监控平台 1=SmartPlus + Indoor Monitor/APT Managment，2=Only SmartPlus，3=Only Indoor Monitor/APT Managment'
const MONITOR_TYPE_SMARTPLUS_AND_INDOOR_MANAGMENT = "1";
const MONITOR_TYPE_ONLY_SMARTPLUS = "2";
const MONITOR_TYPE_ONLY_INDOOR_MANAGMENT = "3";

const VIDEO_STORAGE_ENABLE = 0;
const VIDEO_STORAGE_CANCEL = 1;
const VIDEO_STORAGE_EXPIRE = 2;

// 分配设备类型
const OFFICE_DEVICE_ASSIGN_TYPE_PERSONNEL = 1; // 个人
const OFFICE_DEVICE_ASSIGN_TYPE_GROUP = 2; // 组
const OFFICE_DEVICE_ASSIGN_TYPE_COMPANY = 3; // 公司


// 短信验证码用户类型
const SMS_CODE_USER_TYPE_END_USER = 0;
const SMS_CODE_USER_TYPE_NEW_OFFICE_ADMIN = 1;

// 项目类型(后续web新增表ProjectType统一使用这个)
const NEW_WEB_PROJECT_TYPE_SINGLE = 1;
const NEW_WEB_PROJECT_TYPE_COMMUNITY = 2;
const NEW_WEB_PROJECT_TYPE_OFFICE = 3;

const RELAY_FUNCTION_DOOR = 4;

// 继电器输出类型前缀
const RELAY_OUTPUT_PREFIX_K = 'K';
const RELAY_OUTPUT_PREFIX_OT = 'OT';

// 每个设备的继电器数量
const RELAY_COUNT_PER_EXTERN_DEVICE = 16;

//smartlock mqtt client flag
const SMARTLOCK_MQTT_CLIENT_FLAG_SL20 = "SL20";
const SMARTLOCK_MQTT_CLIENT_FLAG_SL50 = "SL50";
const PREMIUM_SMARTLOCK_CLEINT_FLAG = "PSL-";

const RELAY_TYPE_RSAC_C1_R8 = 3;

const SMARTLOCK_MODLE_SL20 = 1;
const SMARTLOCK_MODLE_SL50 = 2;