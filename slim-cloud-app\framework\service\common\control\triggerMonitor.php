<?php

namespace common\control;
require_once __DIR__ . "/../../common/model/token.php";
require_once __DIR__ . "/../../common/model/devices.php";
require_once __DIR__ . "/../../common/model/personalCapture.php";
require_once __DIR__ . "/../../common/model/personalAccount.php";

function triggerMonitorChain($request, $response)
{
    $msg = $request->getParsedBody()['msg'];
    $type = $request->getParsedBody()['type'];
    $token = $request->getHeaderLine('x-auth-token');
    $timestamp = $request->getParsedBody()['timestamp'];

    if ($type == TRIGGER_REFRESH_CONFIG_CHAIN) {
        return triggerRefreshConfigChain($request, $response);
    }
 
    if ($token === md5("Akuvox:" . $type . ":" . $timestamp . ":2023")) {
        if ($type == TRIGGER_EMAIL_CHAIN) {
            triggerEmailChainNotify($msg);
        } elseif ($type == TRIGGER_THIRDPARTY_CHAIN) {
            triggerThirdpartyChainNotify($msg);
        } elseif ($type == TRIGGER_FTP_UPLOAD_CHAIN) {
            return triggerFtpUploadChain($request, $response);
        } elseif ($type == TRIGGER_CHECKNODE_TIMEZONE) {
            return triggerNodeTimezone($request, $response);
        }  
    } else {
        return \util\response\setResponseMessage($response, ERR_CODE_FAILED, [] ,"token invalid");
    }
    return \util\response\setResponseMessage($response, ERR_CODE_SUCCESS);
}

// ftp picture detect
function triggerFtpUploadChain($request, $response)
{
    $mac = $request->getParsedBody()['mac'];
    $picname = $request->getParsedBody()['picname'];

    // 获取logSlice信息
    $tableSliceInfo = \common\model\getLogDeliveryInfo(LOG_TABLE_CAPTURE);

    $tableIndex = 0;
    // 获取社区id
    $deviceInfo = \common\model\getDevicesInfoByMac($mac);
    if (! $deviceInfo)
    {
         $per_deviceInfo = \common\model\getPersonalDevicesInfoByMac($mac);
         $AccountInfo = \common\model\getUserSipInfo($per_deviceInfo['Node']);
         $tableIndex = \common\model\getLogDbTableIndex($AccountInfo['UUID'], $tableSliceInfo['Delivery']);
    }
    else
    {
        // 获取社区uuid
        $projectInfo = \common\model\getAccountByID($deviceInfo['MngAccountID']);

        // 获取当前log表
        $tableIndex = \common\model\getLogDbTableIndex($projectInfo['UUID'], $tableSliceInfo['Delivery']);
    }

    // 检测picurl
    $capturelogInfo = \common\model\getPersonakCaptureLogInfo($mac, $picname, LOG_TABLE_CAPTURE . '_' . $tableIndex);
    if (!empty($capturelogInfo['PicUrl'] && !empty($capturelogInfo['SPicUrl']))) {
        return \util\response\setResponseMessage($response, ERR_CODE_SUCCESS);
    } else {
        return \util\response\setResponseMessage($response, ERR_CODE_FAILED, [] ,"ftp picture detect failed");
    }
}

// refresh dev config
function triggerRefreshConfigChain($request, $response)
{
    $mac = $request->getParsedBody()['mac'];
    $token = $request->getParsedBody()['token'];
    $account = $request->getParsedBody()['account'];
    $location = $request->getParsedBody()['location'];

    // 校验token
    $tokenInfo = \common\model\getTokenInfoByAppToken($token);

    if ($tokenInfo['Account'] === $account) {
        $accountInfo = \common\model\getCommInfo($account);
        \common\model\updateNodeDevicesLocation($mac, $account, $location);
        webCommunityModifyNotify(WEB_COMM_NODE_UPDATE, $account, $mac, $accountInfo['MngID'], $accountInfo['UnitID']);
        return \util\response\setResponseMessage($response, ERR_CODE_SUCCESS);
    } else {
        return \util\response\setResponseMessage($response, ERR_CODE_FAILED, [] ,"token invalid");
    }
}

// detect hager timezone
function triggerNodeTimezone($request, $response)
{
    $mac = $request->getParsedBody()['mac'];
    $timezone = $request->getParsedBody()['timezone'];

    $per_deviceInfo = \common\model\getPersonalDevicesInfoByMac($mac);
    $accountInfo = \common\model\getUserSipInfo($per_deviceInfo['Node']);

    if ($timezone == $accountInfo["TimeZone"]) {
        return \util\response\setResponseMessage($response, ERR_CODE_SUCCESS);
    } else {
        return \util\response\setResponseMessage($response, ERR_CODE_FAILED, [] ,"node timezone error");
    }
}
