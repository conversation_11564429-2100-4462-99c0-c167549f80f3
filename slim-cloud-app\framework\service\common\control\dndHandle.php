<?php

namespace  common\control;
require_once __DIR__ . "/../model/dnd.php";

function setDnd($request, $response)
{
    $postDatas = $request->getParsedBody();
    $status = (int)$postDatas['status'];
    $startTime = $postDatas['start_time'];
    $endTime = $postDatas['end_time'];
    $startMinutes = \util\utility\timeToMinutes($startTime);
    $endMinutes = \util\utility\timeToMinutes($endTime);

    $userConf = \util\container\getUserData();
    $personalAccount = $userConf['UserAccount'];

    $data = \common\model\queryDND($personalAccount);
    if ($data) {
        \common\model\updateDND($personalAccount, $status, $startMinutes, $endMinutes);
    } else {
        \common\model\insertDND($personalAccount, $status, $startMinutes, $endMinutes);
    }

    $dndData = $status . "-" . $startMinutes . "-" . $endMinutes;
    \util\redisManage\setDnd($userConf['UserAccount'], $dndData);

    if (null == \util\redisManage\getDnd($userConf['UserAccount'])) {
        \util\alarmManage\AddAkcsAlarm(AKCS_MONITOR_ALARM_SET_DND_FAILED, "Set Dnd Info Failed,Account=" . $userConf['UserAccount'] . ";Value is " . $dndData);
    }

    return \util\response\setResponseMessage($response, ERR_CODE_SUCCESS);
}

function getDND($request, $response)
{
    $paramValue = $request->getQueryParams();
    $userConf = \util\container\getUserData();
    $personalAccount = $userConf['UserAccount'];

    $data = \common\model\queryDND($personalAccount);
    if ($data) {
        $datasJson["status"] = strval($data['Status']);
        $datasJson["start_time"] = \util\utility\minuteToTime($data['StartTime']);
        $datasJson['end_time'] = \util\utility\minuteToTime($data['EndTime']);
    } else {
        $datasJson["status"] = "0";
        $datasJson["start_time"] = "23:00";
        $datasJson['end_time'] = "07:00";
    }

    return \util\response\setResponseMessage($response, ERR_CODE_SUCCESS, $datasJson);
}