{"name": "workerman/workerman", "type": "library", "keywords": ["event-loop", "asynchronous"], "homepage": "http://www.workerman.net", "license": "MIT", "description": "An asynchronous event driven PHP framework for easily building fast, scalable network applications.", "authors": [{"name": "walkor", "email": "<EMAIL>", "homepage": "http://www.workerman.net", "role": "Developer"}], "support": {"email": "<EMAIL>", "issues": "https://github.com/walkor/workerman/issues", "forum": "http://wenda.workerman.net/", "wiki": "http://doc.workerman.net/", "source": "https://github.com/walkor/workerman"}, "require": {"php": ">=7.0"}, "suggest": {"ext-event": "For better performance. "}, "autoload": {"psr-4": {"Workerman\\": "./"}}, "minimum-stability": "dev"}