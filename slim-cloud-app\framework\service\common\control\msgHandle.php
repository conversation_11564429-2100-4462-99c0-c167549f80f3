<?php

namespace  common\control;
require_once __DIR__ . "/../../common/model/messageAccountList.php";

function setMsgRead($request, $response) 
{
    $bodyValue = $request->getParsedBody();
    $noticeSwitch = $bodyValue["notice"];

    //通过UserInfoUUID获取所有的site信息
    $userData = \util\container\getUserData();
    $personalAccountList = \common\model\getAllSiteInfoByUserInfoUUID($userData['UserInfoUUID']);

    if($noticeSwitch) {
        if(!\common\model\setMessageRead($personalAccountList)) {
            return \util\response\setResponseMessage($response, ERR_CODE_SUCCESS);
        }
    } else if(!$noticeSwitch) {
        return \util\response\setResponseMessage($response, ERR_CODE_SUCCESS);
    }
    return \util\response\setResponseMessage($response, ERR_CODE_FAILED, [], "set msg read failed");
}