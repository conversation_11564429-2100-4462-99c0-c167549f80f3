<?php
require_once __DIR__ . "/../smartlock/control/devicesStatus.php";
require_once __DIR__ . "/../smartlock/control/smartLockMqttWebhookAuth.php";


$gApp->post('/appbackend_adapt/smarthome_lock/mqtt_webhook/auth', function ($request, $response) {
    $response = \smartlock\control\mqttWebhookAuth($request, $response);
    return $response;
});


$gApp->post('/appbackend_adapt/smarthome_lock/mqtt_webhook/device_status', function ($request, $response) {
    $response = \smartlock\control\mqttDevicesStatus($request, $response);
    return $response;
});
