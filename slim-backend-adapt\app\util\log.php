<?php
namespace util\log;

use support\Log;
function WEBMAN_LOG_TRACE($content)
{
    Log::info($content);
}

//打印response开关
const LOG_DEBUG = 0;

//Log加密开关
const LOG_ENCRYPT_SWITCH = true;

//Log加密字段列表,均为小写
const LOG_ENCRYPT_ARRAY = ["email", "passwd", "contactemail", "firstname", "lastname", "phone", "phone1", "phone2", "phone3", "telephone","fax", "mobilenumber", "payeremail"];

//Log加密后缀
const LOG_ENCRYPT_SUFFIX = "_encrypt";

//Log加密密钥
const LOG_ENCRYPT_SECRET = "mx6pC6U7RFQ8sp38DZMydQJ037yAhVmh";

const IV = "vVhuHEBY4NiccDrj";

// 加密函数
function encryptData($data, $key = LOG_ENCRYPT_SECRET)
{
    return openssl_encrypt($data, 'aes-256-cbc', $key . $data, 0, IV);
}

// 解密函数
function decryptData($encryptedData, $key = LOG_ENCRYPT_SECRET)
{
    return openssl_decrypt($encryptedData, 'aes-256-cbc', $key . $encryptedData, 0, IV);
}

class akcsLog
{
    public static $instance;
    private $traceID;

    private function __clone()
    {
    }

    private function __construct()
    {
        $this->traceID = \util\container\getTraceID();
    }

    public static function getInstance()
    {
        if (!self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    public static function requestReg($request, array $props = [], array $alarmType = [])
    {
        $instance = self::getInstance();

        $ip_addr = $request->getRealIp();

        $request_reg_msg = "ip: $ip_addr uri : {$request->getUri()->getPath()}";

        $now = date('Y-m-d H:i:s', time());

        $message = "{$now} [{$instance->traceID}][register] $request_reg_msg \n";

        WEBMAN_LOG_TRACE($message);
    }

    public static function debug($msg, array $props = [], array $alarmType = [])
    {
        $instance = self::getInstance();

        $msg = $instance->dealMsg($msg, $props);

        $now = date('Y-m-d H:i:s', time());

        $message = "{$now} [{$instance->traceID}][debug] $msg \n";

        WEBMAN_LOG_TRACE($message);
    }

    public static function request($request, array $props = [], array $alarmType = [])
    {
        $instance = self::getInstance();

        $now = date('Y-m-d H:i:s', time());

        $requestMsg = "uri : {$request->getUri()->getPath()}";

        $requestMsg = $requestMsg. " head api-version:" . $request->getHeaderLine('api-version');

        $params = $request->getQueryParams();
        if(count($params) > 0) {
            $requestMsg = $requestMsg ." getParams:";
            foreach ($params as $key => $value) {
                $requestMsg = $requestMsg ." ". $key . ": " .$value;
            }            
        }

        // 上传人脸时不打印postParams不然会打印大量乱码
        if ($request->getUri()->getPath() != "/upload_face") {

            // 检查Content-Type是否为application/json
            $contentType = $request->getHeaderLine('Content-Type');
            $isJson = strpos($contentType, 'application/json') !== false;
            if ($isJson)
            {
                $requestMsg = $requestMsg." postParams:".$request->rawBody();
            }
            else
            {
                $requestMsg = $requestMsg." postParams:";
                $params = $request->getParsedBody();
                foreach ($params as $key => $value) {
                    $requestMsg = $requestMsg." ". $key. ": ".$value;
                }
            }
        }

        $dealMsg =  $instance->dealMsg($requestMsg, $props);

        $message = "{$now} [{$instance->traceID}][request] $dealMsg \n";

        WEBMAN_LOG_TRACE($message);
    }

    public static function response($response)
    {
        if(LOG_DEBUG) {
            $instance = self::getInstance();

            $now = date('Y-m-d H:i:s', time());

            $responseBody = $response->rawBody();
            
            $dealMsg = $instance->dealMsg("body = {body}", ['body'=>$responseBody]);
    
            $message = "{$now} [{$instance->traceID}][response] $dealMsg \n";
    
            WEBMAN_LOG_TRACE($message);
        }
    }

    private function dealMsg($msg, array $props = [])
    {
        if (LOG_ENCRYPT_SWITCH) {
            $msg = $this->dealMsgEncrypt($msg, $props);
        } else {
            $msg = $this->dealMsgNotEncrypt($msg, $props);
        }
        return $msg;
    }
    
    private function dealMsgNotEncrypt($msg, array $props = [])
    {
        $replace = array();
        foreach ($props as $key => $val) {
            $replace['{' . $key . '}'] = var_export($val, true);
        }

        // 替换记录信息中的占位符，最后返回修改后的记录信息。
        $msg = strtr($msg, $replace);
        return $msg;
    }

    private function dealMsgEncrypt($msg, array $props = [])
    {
        $replace = array();
        // 加密数组
        $encrypt = array();

        foreach ($props as $key => $val) {
            //匹配的key如果是关联数组的key时忽略大小写
            $equalKey = is_int($key) ? $key : strtolower($key);
            //如果key已经被标识加密，则对整个value进行加密
            if (in_array($equalKey, LOG_ENCRYPT_ARRAY)) {
                $val = var_export($val, true);
                $encrypt[$key] = "[" . $key . LOG_ENCRYPT_SUFFIX . "]";
                //对整个value进行加密
                $replace['{' . $key . '}'] = '[' . encryptData($val) . ']';
                continue;
            }
            if (is_array($val)) {
                // 如果值是数组，递归处理
                $this->recursiveEncrypt($val);
                $val = var_export($val, true);
            }
            $replace['{' . $key . '}'] = $val;
        }

        // 替换记录信息中的占位符，最后返回修改后的记录信息。
        $msg = strtr($msg, $replace);
        // 加密字段进行标识，加上后缀
        return strtr($msg, $encrypt);
    }

    private function recursiveEncrypt(array &$array, $deep = 0)
    {
        if ($deep > 4) {
            return $array;
        }
        foreach ($array as $key => &$value) {
            if (is_array($value)) {
                // 如果值是数组，则递归处理
                $this->recursiveEncrypt($value, $deep + 1);
            } else {
                //匹配的key如果是关联数组的key时忽略大小写
                $equalKey = is_int($key) ? $key : strtolower($key);
                // 如果值不是数组，则进行加密处理
                if (in_array($equalKey, LOG_ENCRYPT_ARRAY, true)) {
                    $encryptValue = '[' . encryptData($value) . ']';
                    $encryptKey = $key . LOG_ENCRYPT_SUFFIX;
                    $array['[' . $encryptKey . ']'] = $encryptValue;
                    unset($array[$key]);
                }
            }
        }
    }
}
