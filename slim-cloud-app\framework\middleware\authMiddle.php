<?php
require_once(__DIR__ . '/../service/common/model/personalAccount.php');

class AuthMiddle
{
    private $authWhitelist = ['/send_mobile_checkcode', '/trigger_monitor_chain', '/yale/webhook'];
    private $initOptionslist = ['/third_party_lock/get_oauth_url', '/third_party_lock/auth_bind', '/tiandy_register_user'];

    public function __invoke($request, $response, $next)
    {
        //特殊请求过滤
        if(\util\common\isSpecialReq($request))
        {
            return $next($request, $response);
        }

        \util\log\akcsLog::request($request);
        \util\log\akcsLog::requestReg($request);

        //鉴权白名单
        $uri = $request->getUri()->getPath();
        if (in_array($uri, $this->authWhitelist)) {
            \util\container\setUserData(array());
            $response = $next($request, $response);
            return $response;
        }

        //跨域处理
        if (in_array($uri, $this->initOptionslist)) {
            $method = $request->getMethod();
            if ($method === "OPTIONS") {
                $response = \util\response\addCorsHeaders($response);
                $response = \util\response\setThirdLinkerResponseMessage($response, "success", 0);
                return $response;
            }
        }

        //校验身份
        $result = \common\model\checkIdentity();
        if ($result == ERR_TOKEN_INVALID) {
            return \util\response\setResponseMessage($response, ERR_CODE_TOKEN_INVALID);
        }
        if ($result == ERR_TOKEN_EXPIRE) {
            return \util\response\setResponseMessage($response, ERR_CODE_TOKEN_EXPIRE);
        }

        //若带site，则进行权限判断，若属于自己的多套房，则进行切换
        //GET请求site放param中
        $paramValue = $request->getQueryParams();

        $site = '';
        if(isset($paramValue['site'])){
            $site = $paramValue['site'];
        }
        //POST请求site放body中
        $postDatas = $request->getParsedBody();
        if($postDatas['site']){
            $site = $postDatas['site'];
        }
        if($site) {
            $siteInfo = \common\model\getMultiSiteRoomInfo($result['UserInfoUUID']);
            foreach($siteInfo as $info) {
                //site可能是account也可能是node，后续csmain整改后 统一可改为account
                if($info['Account'] == $site || $info['Node'] == $site) {
                    $result['Role'] = $info['Role'];
                    $result['Account'] = $info['Account'];
                    $result['ProjectType'] = $info['ProjectType'];
                    \util\log\akcsLog::debug("param with site, account change to site {$site}");
                    break;
                }
            }
        }

        \util\container\setUserData($result);
        
        $response = $next($request, $response);

        return $response;
    }
}

