<?php
namespace common\control;

require_once __DIR__ . "/../../common/model/thirdPartyLockDevice.php";
require_once __DIR__ . "/../../common/model/thirdPartyLockAccount.php";

function getQrioAuthdata($code, &$authData)
{
    $datas = array();
    $datas['client_id'] = QRIO_CLIENT_ID;
    $datas['code'] = $code;
    $datas['grant_type'] = 'authorization_code';
    $datas['redirect_uri'] = QRIO_REDIRECT_URL;
    $headers=['Content-Type: application/x-www-form-urlencoded'];
    $output = \util\common\httpRequest("post", QRIO_AUTH_URL.'/oauth/token', $headers, $datas);

    $tokenDatas = json_decode($output, true);
    $authData['Token'] = $tokenDatas['id_token'];
    $expires = $tokenDatas['expires_in'];
    $authData['RefreshToken'] = $tokenDatas['refresh_token'];
    $authData['ExpireTime'] = date('Y-m-d H:i:s', time()+$expires);
}

function getQrioHash($token, $authInfo, $needRefresh)
{
    if (!$token)
    {
        return false;
    }

    $headers = ["Authorization: Bearer ".$token];
    $output = \util\common\httpRequest("get", QRIO_API_URL.'/hash', $headers);
    $outputArr = json_decode($output, true);
    $hash = $outputArr['hash'];

    //token过期
    $errorInfo = $outputArr['error'];
    if (strstr($errorInfo, 'expire') && $needRefresh) {
        $newIdToken = refreshQrioToken($authInfo['UUID'], $authInfo['RefreshToken']);
        if ($newIdToken) {
            $token = $newIdToken;
            $headers = ["Authorization: Bearer ".$token];
            $output = \util\common\httpRequest("get", QRIO_API_URL.'/hash', $headers);
            $outputArr = json_decode($output, true);
            $hash = $outputArr['hash'];
        } else {
            //刷新token失败才返回false
            return false;
        }
    }

    if ($hash) {
        return $hash;
    }
    return false;
}

function getQrioLock($token, $hash, &$data, &$thirdUuidArr)
{
    if (!$hash  || !$token)
    {
        \util\log\akcsLog::debug("get_qrio_lock token or hash is null!");
        return;
    }
    $headers = ["Authorization: Bearer ".$token, "Content-Type: application/x-www-form-urlencoded"];
    $output2 = \util\common\httpRequest("get", QRIO_API_URL.'/all?ed_pk='.$hash, $headers);
    \util\log\akcsLog::debug("get master_key qrio: ".$output2);
    $outputArr2 = json_decode($output2, true);
    $lockDatas1 = $outputArr2['data'];
    getQrioLockData($lockDatas1, $data, $thirdUuidArr, $headers);

    $output2 = \util\common\httpRequest("get", QRIO_API_URL.'/roomon/all_key?client_ed_pk='.$hash, $headers);
    \util\log\akcsLog::debug("get resident_keys qrio: ".$output2);
    $outputArr2 = json_decode($output2, true);
    $lockDatas2 = $outputArr2['resident_keys'];
    getQrioLockData($lockDatas2, $data, $thirdUuidArr, $headers);
}

function getQrioLockData($lockDatas, &$data, &$thirdUuidArr, $headers) {
    $i = count($data);
    foreach ($lockDatas as $lockInfo) {
        $data[$i]['LockName'] = $lockInfo['lock']['lock_setting']['lock_name'];
        // 如果是主锁，获取 master_key_id，否则获取 resident_key_id
        if (isset($lockInfo['master_key'])) {
            $data[$i]['DeviceUUID'] = $lockInfo['master_key']['id'];
        } else {
            $data[$i]['DeviceUUID'] = $lockInfo['resident_key_id'];
        }
        $data[$i]['LockID'] = $lockInfo['lock']['id'];
        $thirdUuidArr[$i] = $data[$i]['DeviceUUID'];

        // 获取锁开关状态
        $output3 = \util\common\httpRequest("get", QRIO_API_URL.'/locks/'.$data[$i]['LockID'].'/thumbturn_state', $headers);
        $outputArr3 = json_decode($output3, true);
        $status = $outputArr3['main_lock'];
        //2 closed 1 open
        if ($status == 1) {
            $data[$i]['LockStatus'] = 1;
        } else {
            $data[$i]['LockStatus'] = 0;
        }
        $i++;
    }
}

function refreshQrioToken($uuid, $refreshToken)
{
    $headers = [];
    $data = [];
    $data['grant_type'] = "refresh_token";
    $data['client_id'] = QRIO_CLIENT_ID;
    $data['refresh_token'] = $refreshToken;
    $url = QRIO_AUTH_URL."/oauth/token";
    $output = \util\common\httpRequest('post', $url, $headers, $data);
    $outputArr = json_decode($output, true);

    if ($outputArr['id_token']) {
        if ($outputArr['refresh_token']) {
            $refreshToken = $outputArr['refresh_token'];
        }
        $idToken = $outputArr['id_token'];
        $expireTime = date('Y-m-d H:i:s', time()+36000);
        \common\model\updateThirdPartyLockToken($refreshToken, $idToken, $expireTime, $uuid);
    } else {
        \util\log\akcsLog::debug($uuid." refresh failed");
        return false;
    }
    \util\log\akcsLog::debug($uuid." refresh success, new token: ".$outputArr['id_token']);
    return $outputArr['id_token'];
}

function addQrioLock($userConf, $token)
{
    $authinfo = array();
    $hash = getQrioHash($token, $authinfo, false);
    $data = array();
    $thirdUuidArr = array();
    getQrioLock($token, $hash, $data, $thirdUuidArr);
    foreach ($data as $key => $value)
    {
        $lockName = $value['LockName'];
        $devUuid = $value['DeviceUUID'];
        $lockStatus = $value['LockStatus'];
        
        $uuid = \util\common\getMysqlUUID();
        $lockType = QRIO_LOCK_TYPE;

        \common\model\addThirdPartyLockDevice($userConf, $devUuid, $uuid, $lockType, $lockName, $lockStatus);    
    }
}

function openQrioDoor($userConf, $devUUID, $operateType)
{
    $thirdLockData = array();

    if ($operateType) {
        $thirdLockData['message_type'] = LINKER_MSG_TYPE_QRIO_OPEN_DOOR;
        $thirdLockData['capture_type'] = THIRD_PARTY_LOCK_CAPTURE_TYPE_REMOTE_OPEN_DOOR;
    } else {
        $thirdLockData['message_type'] = LINKER_MSG_TYPE_QRIO_CLOSE_DOOR;
        $thirdLockData['capture_type'] = THIRD_PARTY_LOCK_CAPTURE_TYPE_LOCK_BY_APP;
    }

    $thirdLockData['lock_type'] = QRIO_LOCK_TYPE;
    $thirdLockData['uuid'] = $devUUID;
    $thirdLockData['personal_account_uuid'] = $userConf['NodeUUID'];
    $thirdLockData['initiator'] = $userConf['Name'];
    $thirdLockData['lock_name'] = "";
    
    openThirdPartyLockNotify($thirdLockData);
}

function GetAndUpdateQrioLockinfo($userConf, &$qrioLockList)
{
    $authInfo = \common\model\getThirdPartyLockInfo($userConf['NodeUUID'], QRIO_LOCK_TYPE);
    $token = $authInfo['Token'];
    if ($token) {
        $hash = getQrioHash($token, $authInfo, true);
        if ($hash) {
            $data = array();
            $thirdUuidArr = array();
            getQrioLock($token, $hash, $data, $thirdUuidArr);
            \common\model\updateThirdPartyDevlist($userConf, $data, $thirdUuidArr, QRIO_LOCK_TYPE);
        }
        else {
            \util\log\akcsLog::debug("qrio user expire, NodeUUID:".$userConf['NodeUUID']);
            return false;
        }
    }
    \common\model\getThirdPartyDevList($userConf, $qrioLockList);
    return true;
}


