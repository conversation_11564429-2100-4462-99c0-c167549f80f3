<?php

namespace smartlock\model;

require_once __DIR__ . "/../../../util/common.php";
// 获取所有lock
function getLockInfo($lockUUID)
{
    return \util\container\medooDb()->get(
        "SmartLock",
        ["UUID", "MqttPwd", "MAC"],
        ["UUID" => $lockUUID]
    );
}

function SmartLockMqttAuthCheck($mac, $clientId, $password)
{
    $lockInfo = \util\container\medooDb()->get(
            "SmartLock",
            ["MqttPwd"],
            ["UUID" => $clientId, "MAC" => $mac]
    );
    if($lockInfo && (\util\utility\passwdDecode($lockInfo['MqttPwd']) == $password))
    {
        return true;
    }

    return false;
}

/**
 * 设备上线时更新状态
 * 
 * @param string $clientId 设备UUID
 * @param string $ipAddress 设备IP地址
 * @return bool 更新是否成功
 */
function updateDeviceOnline($clientId, $ipAddress, $online_time)
{
    try {
        // 使用medooDb()->update()方法更新数据
        $result = \util\container\medooDb()->update(
            "SmartLock", 
            [
                "Status" => 1, 
                "outerIP" => $ipAddress,
                "LastConnectedTime" => $online_time
            ], 
            ["UUID" => $clientId]
        );
        
        return $result->rowCount() > 0;
    } catch (\Exception $e) {
        \util\log\akcsLog::debug("更新设备在线状态失败: " . $e->getMessage());
        return false;
    }
}

/**
 * 设备离线时更新状态
 * 
 * @param string $clientId 设备UUID
 * @return bool 更新是否成功
 */
function updateDeviceOffline($clientId)
{
    try {
        // 使用medooDb()->update()方法更新数据
        $result = \util\container\medooDb()->update(
            "SmartLock", 
            [
                "Status" => 0,
                "LastDisConn" => \util\medoo\Medoo::raw("NOW()")
            ], 
            ["UUID" => $clientId]
        );
        
        return $result->rowCount() > 0;
    } catch (\Exception $e) {
        \util\log\akcsLog::debug("更新设备离线状态失败: " . $e->getMessage());
        return false;
    }
}