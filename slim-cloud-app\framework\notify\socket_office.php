<?php

require_once(dirname(__FILE__) . '/notify_office.php');
require_once(dirname(__FILE__) . '/../config/define.php');
require_once(dirname(__FILE__) . '/proto/proto_office.php');
require_once(dirname(__FILE__) . '/socket.php');

class CWebOfficeModifyNotify extends CSocket
{
    public function copy($data)
    {
        if (is_string($data) || is_int($data) || is_float($data)) {
            $data[] = $data;
        }
        if (is_array($data)) {
            $TempData = new AK\AdaptOffice\WebOfficeModifyNotify();
            $TempData->setChangeType((int) $data[0]);
            $TempData->setNode((string) $data[1]);
            $macs = explode(";", $data[2]);
            $TempData->setMacList($macs);
            $TempData->setOfficeId((int) $data[3]);
            $TempData->setDepartmentId((int) $data[4]);
            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}


//办公场景
class COfficeAccountRenewSocket extends CSocket
{
    public function copy($data)
    {
        if (is_string($data)||is_int($data)||is_float($data)) {
            $data[]=$data;
        }
        if (is_array($data)) {
            $TempData = new AK\AdaptOffice\PMOfficeAccountRenew();
            $TempData->setCommunity($data[0]);
            $TempData->setEmail($data[1]);
            $TempData->setPmName($data[2]);
            $TempData->setAccountNum($data[3]);
            $TempData->setList($data[4]);
            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}

