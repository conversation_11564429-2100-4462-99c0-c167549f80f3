<?php

namespace common\model;

use PDO;

function checkFaceFlag($node)
{
    $db =  \util\container\getDb();
    $sth = $db->prepare("select Flags from PersonalAccountCnf where Account = :account");
    $sth->bindParam(':account', $node, PDO::PARAM_STR);
    $sth->execute();
    $result = $sth->fetch(PDO::FETCH_ASSOC);
    if ($result) {
        return \util\utility\switchHandle($result['Flags'], PersonlAccountCnfFlagsFace);
    }
    return 0;
}

function getMotionStatus($account, &$enablMotion, &$motionTime)
{
    $db =  \util\container\getDb();
    $sth = $db->prepare("/*master*/ select EnableMotion, MotionTime from PersonalAccountCnf where Account = :account");
    $sth->bindParam(':account', $account, PDO::PARAM_STR);
    $sth->execute();
    $result = $sth->fetch(PDO::FETCH_ASSOC);

    $enablMotion = $result['EnableMotion'];
    $motionTime = $result['MotionTime'];
}

function updateMotionStatus($motionTime, $enableMotion, $userAccount)
{
    $db =  \util\container\getDb();
    $sth = $db->prepare("UPDATE PersonalAccountCnf SET EnableMotion = :enable_motion, MotionTime = :motion_time WHERE Account = :account");
    $sth->bindParam(':motion_time', $motionTime, PDO::PARAM_INT);
    $sth->bindParam(':enable_motion', $enableMotion, PDO::PARAM_INT);
    $sth->bindParam(':account', $userAccount, PDO::PARAM_STR);
    $sth->execute();
}

function getCalltypeByAccount($account)
{       
    $db =  \util\container\getDb();
    $sth = $db->prepare("/*master*/ SELECT CallType,EnableRobinCall FROM PersonalAccountCnf WHERE Account = :account");
    $sth->bindParam(':account', $account, PDO::PARAM_STR);
    $sth->execute();
    $result = $sth->fetch(PDO::FETCH_ASSOC);
    
    return $result;
}

function updateCalltype($userConf, $calltype)
{
    $db = \util\container\getdb();

    $db->beginTransaction();

    $sthRole = $db->prepare("update PersonalAccountCnf set CallType=:Calltype  WHERE Account = :account");
    $sthRole->bindParam(':account', $userConf['Account'], PDO::PARAM_STR);
    $sthRole->bindParam(':Calltype', $calltype, PDO::PARAM_STR);
    $sthRole->execute();

    \common\model\updatePhoneStatusByCalltype($calltype, $userConf['Account']);

    $sthSip = $db->prepare("select SipAccount From PersonalAccount where ParentID=:AccountID and (Role=" . ROLE_TYPE_PERSONNAL_SLAVE . " or Role=" . ROLE_TYPE_COMMUNITY_SLAVE . ") union all select SipAccount  From PersonalAccount where ID=:AccountID;");
    $sthSip->bindParam(':AccountID', $userConf['AccountID'], PDO::PARAM_STR);
    $sthSip->execute();
    $Sips = $sthSip->fetchALL(PDO::FETCH_ASSOC);

    $enableGroup = $calltype == 1 ? "0" : "1";

    foreach ($Sips as $value) {
        $sthSip = $db->prepare("insert LocalSipTransaction(Sip,Message) values(:Sip,:Message);");
        $sthSip->bindParam(':Sip', $value["SipAccount"], PDO::PARAM_STR);
        $sthSip->bindParam(':Message', json_encode(["messageType"=>"1","sip"=>strval($value["SipAccount"]),"groupring"=>strval($enableGroup)]), PDO::PARAM_STR);
        $sthSip->execute();
    }

    $db->commit();
}

function checkIDAccessFlag($node)
{
    $result =  \util\container\medooDb()->get("PersonalAccountCnf", ["Flags"], ["Account" => $node]);

    if ($result) {
        return \util\utility\switchHandle($result['Flags'], PersonlAccountCnfFlagsIDAccess);
    }
    return 0;
}

function checkRfCardFlag($node)
{
    $result =  \util\container\medooDb()->get("PersonalAccountCnf", ["Flags"], ["Account" => $node]);

    if ($result) {
        return \util\utility\switchHandle($result['Flags'], PersonlAccountCnfFlagsRfCard);
    }
    return 0;
}

function updatePersonalPackageDetectionStatus($enablePackageDetection, $userAccount)
{
    $db =  \util\container\getDb();
    $sth = $db->prepare("UPDATE PersonalAccountCnf SET EnablePackageDetection = :EnablePackageDetection WHERE Account = :Account");
    $sth->bindParam(':EnablePackageDetection', $enablePackageDetection, PDO::PARAM_INT);
    $sth->bindParam(':Account', $userAccount, PDO::PARAM_STR);
    $sth->execute();
}

function updatePersonalSoundDetectionStatus($enableSoundDetection, $soundType, $userAccount)
{
    $db =  \util\container\getDb();
    $sth = $db->prepare("UPDATE PersonalAccountCnf SET EnableSoundDetection = :EnableSoundDetection, SoundType = :SoundType WHERE Account = :Account");
    $sth->bindParam(':EnableSoundDetection', $enableSoundDetection, PDO::PARAM_INT);
    $sth->bindParam(':SoundType', $soundType, PDO::PARAM_INT);
    $sth->bindParam(':Account', $userAccount, PDO::PARAM_STR);
    $sth->execute();
}
function getDetectionInfo($account)
{
    $db =  \util\container\getDb();
    // EnableSoundDetection, SoundType暂时不上
    $sth = $db->prepare("/*master*/ select EnableMotion, MotionTime, EnablePackageDetection from PersonalAccountCnf where Account = :account");
    $sth->bindParam(':account', $account, PDO::PARAM_STR);
    $sth->execute();
    $result = $sth->fetch(PDO::FETCH_ASSOC);

    return $result;
}

function getPersonalAccountCnf($account)
{
    return \util\container\medooDb()->get(
        "PersonalAccountCnf",
        ["EnableMotion", "MotionTime", "EnableRobinCall", "RobinCallTime", "RobinCallVal", "EnableLandline", "WithIndoorMonitor", "EnableSmartHome"],
        ["Account" => $account]
    );
}

function getEnableLandlineService($account)
{
    $db =  \util\container\getDb();
    $sth = $db->prepare("/*master*/ select EnableLandlineService from PersonalAccountCnf where Account = :account");
    $sth->bindParam(':account', $account, PDO::PARAM_STR);
    $sth->execute();
    $result = $sth->fetch(PDO::FETCH_ASSOC);
    return $result['EnableLandlineService'];
}