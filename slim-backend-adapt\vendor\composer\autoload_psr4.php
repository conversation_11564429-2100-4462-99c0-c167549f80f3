<?php

// autoload_psr4.php @generated by Composer

$vendorDir = dirname(dirname(__FILE__));
$baseDir = dirname($vendorDir);

return array(
    'support\\' => array($vendorDir . '/workerman/webman-framework/src/support'),
    'Workerman\\' => array($vendorDir . '/workerman/workerman'),
    'Webman\\' => array($vendorDir . '/workerman/webman-framework/src'),
    'Support\\View\\' => array($vendorDir . '/workerman/webman-framework/src/support/view'),
    'Support\\Exception\\' => array($vendorDir . '/workerman/webman-framework/src/support/exception'),
    'Support\\Bootstrap\\' => array($vendorDir . '/workerman/webman-framework/src/support/bootstrap'),
    'Support\\' => array($vendorDir . '/workerman/webman-framework/src/support'),
    'Psr\\Log\\' => array($vendorDir . '/psr/log/Psr/Log'),
    'Psr\\Container\\' => array($vendorDir . '/psr/container/src'),
    'Monolog\\' => array($vendorDir . '/monolog/monolog/src/Monolog'),
    'FastRoute\\' => array($vendorDir . '/nikic/fast-route/src'),
    'App\\' => array($baseDir . '/app'),
    '' => array($baseDir . '/'),
);
