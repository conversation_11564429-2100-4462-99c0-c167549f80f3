<?php return array(
    'root' => array(
        'pretty_version' => 'v1.4.3',
        'version' => '1.4.3.0',
        'type' => 'project',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'reference' => NULL,
        'name' => 'workerman/webman',
        'dev' => true,
    ),
    'versions' => array(
        'monolog/monolog' => array(
            'pretty_version' => '2.9.2',
            'version' => '2.9.2.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../monolog/monolog',
            'aliases' => array(),
            'reference' => '437cb3628f4cf6042cc10ae97fc2b8472e48ca1f',
            'dev_requirement' => false,
        ),
        'nikic/fast-route' => array(
            'pretty_version' => 'v1.3.0',
            'version' => '1.3.0.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../nikic/fast-route',
            'aliases' => array(),
            'reference' => '181d480e08d9476e61381e04a71b34dc0432e812',
            'dev_requirement' => false,
        ),
        'psr/container' => array(
            'pretty_version' => '2.0.2',
            'version' => '2.0.2.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/container',
            'aliases' => array(),
            'reference' => 'c71ecc56dfe541dbd90c5360474fbc405f8d5963',
            'dev_requirement' => false,
        ),
        'psr/log' => array(
            'pretty_version' => '1.1.4',
            'version' => '1.1.4.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/log',
            'aliases' => array(),
            'reference' => 'd49695b909c3b7628b6289db5479a1c204601f11',
            'dev_requirement' => false,
        ),
        'psr/log-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0.0 || 2.0.0 || 3.0.0',
            ),
        ),
        'workerman/webman' => array(
            'pretty_version' => 'v1.4.3',
            'version' => '1.4.3.0',
            'type' => 'project',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'reference' => NULL,
            'dev_requirement' => false,
        ),
        'workerman/webman-framework' => array(
            'pretty_version' => 'v1.5.13',
            'version' => '1.5.13.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../workerman/webman-framework',
            'aliases' => array(),
            'reference' => '699c9c9509d472679fa1d8461b9a9bb58fbe79a7',
            'dev_requirement' => false,
        ),
        'workerman/workerman' => array(
            'pretty_version' => 'v4.1.14',
            'version' => '4.1.14.0',
            'type' => 'library',
            'install_path' => __DIR__ . '/../workerman/workerman',
            'aliases' => array(),
            'reference' => 'f7c9667c7b5387c01fa9e50ee79ed931e93ee76e',
            'dev_requirement' => false,
        ),
    ),
);
