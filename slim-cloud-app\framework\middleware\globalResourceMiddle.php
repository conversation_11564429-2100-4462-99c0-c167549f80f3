<?php

use util\medoo\Medoo;

class GlobalResourceMiddle
{
    public function __invoke($request, $response, $next)
    {
        global $gApp, $gContainer;
        $gContainer = $gApp->getContainer();
        $gContainer['akcs_trace_id'] = \util\utility\createTraceID(16);
        $gContainer['akcs_medoo_db'] = $this->initMedooDb();
        $gContainer['akcs_db'] = $this->initDb();
        $gContainer['akcs_log_db'] = $this->initLogDb();
        $gContainer['akcs_mapping_db'] = $this->initMappingDb();
        $gContainer['akcs_redis'] = $this->initRedis();
        $gContainer['akcs_audit_log'] = $this->initAuditLog();
        $gContainer['akcs_api_version'] = $request->getHeaderLine('api-version');
        $gContainer['akcs_token'] = $this->initToken($request);
        $gContainer['akcs_user_data'] = [];

        /*
        if($gContainer->has('akcs_response_data')){
            \util\response\setResponseMessage($response, ERR_CODE_SUCCESS);
        }
        */
        return $next($request, $response);
    }

    private function initMedooDb() {
        $database = new \util\medoo\Medoo([
            'database_name' => 'AKCS',
            'server' => AKCS_DATABASEIP,
            'port' => AKCS_DATABASEPORT,
            'username' => 'dbuser01',
            'password' => DATABASEPWD
        ]);
        return $database;
    }

    private function initDb() {
        return \util\container\medooDb()->pdo;
    }

    private function initLogDb() {
        $dbuser = "dbuser01";
        $dbpass = DATABASEPWD;
        $dbip = LOG_DATABASEIP;
        $dbport = LOG_DATABASEPORT;
    
        $mysql_conn_string = "mysql:host=$dbip;port=$dbport;dbname=LOG";
        $dbConnection = new PDO($mysql_conn_string, $dbuser, $dbpass);
        $dbConnection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        $dbConnection->query('set names utf8;');
        return $dbConnection;
    }

    private function initMappingDb() {
        /*
        $dbuser = "dbuser01";
        $dbpass = DATABASEPWD;
        $dbip = AKCS_DATABASEIP;
        $dbport = AKCS_DATABASEPORT;
    
        $mysql_conn_string = "mysql:host=$dbip;port=$dbport;dbname=AKCSMapping";
        $dbConnection = new PDO($mysql_conn_string, $dbuser, $dbpass);
        $dbConnection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        $dbConnection->query('set names utf8;');
        return $dbConnection;
        */
    }

    private function initRedis() {
        $RedisManage = new util\redisManage\RedisManage();
        return $RedisManage->getRedisInstance();
    }

    private function initAuditLog()
    {
        return new \util\auditlog\AuditLog();
    }

    private function initToken($request)
    {
        if ($request->getQueryParams()['token']) {
            return $request->getQueryParams()['token'];
        } else if ($request->getHeaderLine('token')) {
            return $request->getHeaderLine('token');
        } else {
            return $request->getHeaderLine('x-auth-token');
        }
    }
}
