<?php

namespace  resident\control;

require_once __DIR__ . "/../model/sl20Lock.php";
require_once __DIR__ . "/../../common/model/personalDevices.php";
require_once __DIR__ . "/../../common/model/devices.php";
use PDO;

function getSL20LockInfo($uuid)
{
    $db = \util\container\getDB();
    $sth = $db->prepare("SELECT PersonalAccountUUID, KeepAlive FROM SL20Lock where UUID = :UUID");
    $sth->bindParam(':UUID', $uuid, PDO::PARAM_STR);
    $sth->execute();
    $lockInfo = $sth->fetch(PDO::FETCH_ASSOC);
    return $lockInfo;
}

function isSL20Online($lockInfo)
{
    if (empty($lockInfo['LastConnectedTime'])) {
        return 0;
    }

    // 将日期时间字符串转换为时间戳
    $updateTime = strtotime($lockInfo['LastConnectedTime']);
    $currentTime = time();

    // 检查时间差是否小于 24 小时（86400 秒）
    return ($currentTime - $updateTime) < SECONDS_IN_A_DAY;
}

function getLinkDeviceInfo($lockInfo, $isPER)
{
    $lockLinkDeviceInfo = [];
    if (!$lockInfo["DeviceUUID"]) {
        
        return $lockLinkDeviceInfo;
    }

    if ($isPER)
    {
        $bindDeviceInfo = \common\model\getPersonalDevicesInfoByUUID($lockInfo["DeviceUUID"]);
    }
    else
    {
        $bindDeviceInfo = \common\model\getDevicesInfoByUUID($lockInfo["DeviceUUID"]);
    }
    
    $lockLinkDeviceInfo["mac"] = $bindDeviceInfo['MAC'] ? strval($bindDeviceInfo['MAC']) : "";
    $lockLinkDeviceInfo["relay_id"] = intval(\util\utility\bitValueToIndex($lockInfo["Relay"]));
   
    return $lockLinkDeviceInfo;
}

function openSL20Door($request, $response)
{
    $postDatas = $request->getParsedBody();
    $userConf = \util\container\getUserData();
    $uuid = $postDatas['lock_uuid'];
    $sl20Info = getSL20LockInfo($uuid);
    //判断lock_uuid和对应的用户token之间的权限关系
    if (!$sl20Info)
    {
        return \util\response\setResponseMessage($response, ERR_CODE_SL20_NOT_EXIST);
    }
    if ($sl20Info['PersonalAccountUUID'] == $userConf['NodeUUID']) {
        $redis = \util\container\getRedis();
        $redis->select(REDIS_DB_SL2O_LOCK);
        $openDoorRelat = "initiator_" . $userConf['UserAccount'];
   
        if ($redis->hset($uuid, $userConf['UserAccount'], $openDoorRelat)) {
            // 设置过期时间
            $redis->expire($uuid, SL2O_LOCK_OPEN_EXPIRE_TIME);
            
            if ($sl20Info['KeepAlive']) {
                // 若开启保活，将消息推送给锁
                smartlockUpdateNotify($uuid, SL20_LOCK_TYPE);
            }

            return \util\response\setResponseMessage($response, ERR_CODE_SUCCESS);
        } else {
            \util\log\akcsLog::debug("openSL20Door: Failed to set Redis key");
        }
    }
    return \util\response\setResponseMessage($response, ERR_CODE_NO_PERMISSION);
}

function exitOpenSL20Door($request, $response)
{
    $postDatas = $request->getParsedBody();
    $userConf = \util\container\getUserData();

    $uuid = $postDatas['lock_uuid'];
    $sl20Info = getSL20LockInfo($uuid);
    if (!$sl20Info)
    {
        return \util\response\setResponseMessage($response, ERR_CODE_SL20_NOT_EXIST);
    }
    if ($sl20Info['PersonalAccountUUID'] == $userConf['NodeUUID']) {
        $redis = \util\container\getRedis();
        $redis->select(REDIS_DB_SL2O_LOCK);
        if ($redis->hdel($uuid, $userConf['UserAccount']) > 0) {
            if ($sl20Info['KeepAlive']) {
                // 若开启保活，将消息推送给锁
                smartlockUpdateNotify($uuid, SL20_LOCK_TYPE);
            }
            return \util\response\setResponseMessage($response, ERR_CODE_SUCCESS);
        } else {
            \util\log\akcsLog::debug("exitOpenSL20Door: Failed to delete Redis key");
        }
    }
    return \util\response\setResponseMessage($response, ERR_CODE_NO_PERMISSION);
}

function getSL20LockList($request, $response)
{
    $userConf = \util\container\getUserData();
    $lockList = \resident\model\getSL20LockList($userConf["NodeUUID"]);
    if (empty($lockList)) {
        return \util\response\setResponseMessage($response, ERR_CODE_SUCCESS);
    }

    $lockDevList = [];
    $i = 0;
    foreach ($lockList as $lockInfo) {
        $lockDevList[$i]['device_style'] = strval("SL20");
        $lockDevList[$i]['uuid'] = strval($lockInfo['UUID']);
        
        if (isSL20Online($lockInfo)) {
            $lockDevList[$i]['online'] = strval("1");
        } else {
            $lockDevList[$i]['online'] = strval("0");
        }
        $lockDevList[$i]['wifi_status'] = $lockInfo['WifiStatus'] ? strval($lockInfo['WifiStatus']) : strval("0");
        $lockDevList[$i]['battery_level'] = $lockInfo['BatteryLevel'] ? intval($lockInfo['BatteryLevel']): 0;
        $bondedDevices = getLinkDeviceInfo($lockInfo, $userConf['IsPer']);
        if (!empty($bondedDevices) && $bondedDevices["mac"]) {
            $lockDevList[$i]['bonded_devices'] = $bondedDevices;
        } 
        
        $lockDevList[$i]['name'] = strval($lockInfo['Name']);
        $lockDevList[$i]['keep_alive'] = intval($lockInfo['KeepAlive']);
        $i++;
    }

    $datas = [
        "lock_dev_list" => $lockDevList,
    ];
    return \util\response\setResponseMessage($response, ERR_CODE_SUCCESS, $datas);
}
