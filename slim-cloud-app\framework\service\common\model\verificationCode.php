<?php

namespace common\model;

use PDO;

function updateVerificationCode($account, $code)
{
    $db = \util\container\getDb();
    $sth = $db->prepare("insert into VerificationCode (Account,Code) values (:account,:code) ON DUPLICATE KEY update Code = :code, CreateTime = now()");
    $sth->bindParam(':account', $account, PDO::PARAM_STR);
    $sth->bindParam(':code', $code, PDO::PARAM_STR);
    $sth->execute();
}