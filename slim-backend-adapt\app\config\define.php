<?php

ini_set('date.timezone', 'Asia/Shanghai');
require_once(dirname(__FILE__).'/dynamic_config.php');
require_once(dirname(__FILE__).'/confusionFields.php');

const LOG_FILE = "/var/log/php/slim-backend.log";
const REQ_LOG_FILE = "/var/log/php/slim-backend-request.log";

//smartlock mqtt client flag
const SMARTLOCK_MQTT_CLIENT_FLAG_SL20 = "SL20";
const SMARTLOCK_MQTT_CLIENT_FLAG_SL50 = "SL50";
//高级锁的标识
const PREMIUM_SMARTLOCK_CLEINT_FLAG = "PSL-";

const CSSMARTLOCK_USERNAME = 'akcs';
const CSSMARTLOCK_PASSWORD = 'mqttAk20#24!ypt';

// MQTT API配置常量
const MQTT_API_USERNAME = "akuvox";
const MQTT_API_PASSWORD = "aK3wM2zE4iS4jD3gG6oE6hU4nA6zO7dM";
const MQTT_API_TIMEOUT = 5; // 超时时间（秒）