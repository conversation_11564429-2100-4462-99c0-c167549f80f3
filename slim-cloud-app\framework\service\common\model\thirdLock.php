<?php

namespace common\model\thirdLock;

use PDO;

const DB_THIRD_LOCK_RELATE_INFO_TABLE = 'ThirdLockRelateInfo';
const DB_THIRD_LOCK_RELATE_INFO_FIELD = [
    'ID',
    'Brand',
    'Active',
    'ActiveTime',
    'ExpireTime',
    'LockUUID',
    'ProjectType',
    'AccountUUID',
    'PersonalAccountUUID',
    'UUID',
    'CreateTime',
    'UpdateTime'
];

const DB_THIRD_LOCK_ACCESS_GROUP_TABLE = 'AccessGroupThirdLock';
const DB_THIRD_LOCK_ACCESS_GROUP_FIELD = [
    'AccessGroupThirdLock.ID',
    'AccessGroupThirdLock.UUID',
    'AccessGroupThirdLock.AccessGroupID',
    'AccessGroupThirdLock.LockUUID',
    'AccessGroupThirdLock.Brand',
    'AccessGroupThirdLock.CreateTime',
    'AccessGroupThirdLock.UpdateTime'
];


function getThirdLockRelateInfo($lockUUID, $brand)
{
    return \util\container\medooDb()->get(
        DB_THIRD_LOCK_RELATE_INFO_TABLE,
        DB_THIRD_LOCK_RELATE_INFO_FIELD,
        ['LockUUID' => $lockUUID, 'Brand' => $brand]
    );
}


function getAccessGroupThirdLock($lockUUID, $brand, $user)
{
    return \util\container\medooDb()->get(
        DB_THIRD_LOCK_ACCESS_GROUP_TABLE,
        ['[><]AccountAccess' => ['AccessGroupID' => 'AccessGroupID']],
        DB_THIRD_LOCK_ACCESS_GROUP_FIELD,
        ['AND' => ['AccessGroupThirdLock.LockUUID' => $lockUUID, 'AccessGroupThirdLock.Brand' => $brand, 'AccountAccess.Account' => $user]]
    );
}

function getThirdLockPayActiveStatus($lockUUID, $brand)
{
    $payActive = 0;
    $relateInfo = getThirdLockRelateInfo($lockUUID, $brand);
    if($relateInfo && $relateInfo['Active']) {
        $nowTime = time();
        $expireTime = strtotime($relateInfo['ExpireTime']);
        if($expireTime > $nowTime) {
            $payActive = 1;
        }
    }
    return $payActive;
}



