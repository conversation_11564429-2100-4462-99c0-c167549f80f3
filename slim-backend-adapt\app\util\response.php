<?php

namespace util\response;
use support\Response;


function internalSetNewSpecificResponseMessage($response, $err_code, $datas = [], $message = '')
{
    $response_data = [
        'err_code' => strval($err_code),
        'message' => $message,
    ];

    if (!empty($datas)) {
        $response_data['datas'] = $datas;
    }

    return json($response_data);
}

function setResponseMessage($response, $err_code = 0, $datas = [], $message = '')
{
    return internalSetNewSpecificResponseMessage($response, $err_code, $datas, $message);
}

function addCorsHeaders($response)
{
    $response = $response->withStatus(200);
    $response = $response->withHeader('Access-Control-Allow-Headers', 'x-auth-token,x-community-id,x-cloud-lang,x-cloud-version');
    $response = $response->withHeader('Access-Control-Allow-Origin', "*");
    $response = $response->withHeader('Access-Control-Allow-Methods', 'GET,POST,PUT,DELETE,OPTIONS');
    return $response;
}