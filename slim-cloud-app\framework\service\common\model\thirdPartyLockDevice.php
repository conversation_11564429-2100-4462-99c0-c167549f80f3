<?php
namespace common\model;

use PDO;

function getThirdPartyDevList($resultToken, &$thirdPartyDevList)
{
    //以下锁都是房间内的，绑定的设备也是房间内的
    $db = \util\container\getDb();
    $sth = $db->prepare("select LockType,LockName,UUID,LockStatus,MAC,Relay,DoorStatus from ThirdPartyLockDevice where PersonalAccountUUID = :uuid");
    $sth->bindParam(':uuid', $resultToken['NodeUUID'], PDO::PARAM_STR);
    $sth->execute();
    $locks = $sth->fetchALL(PDO::FETCH_ASSOC);

    //qrio和yale锁列表
    $lockDevList = array();
    $i = 0;
    foreach ($locks as $lockInfo) {
        $lockDevList[$i]['type'] = intval($lockInfo['LockType']);
        if ($lockInfo['LockType'] == QRIO_LOCK_TYPE) {
            $lockDevList[$i]['brand'] = "Qrio";
        } elseif ($lockInfo['LockType'] == YALE_LOCK_TYPE) {
            $lockDevList[$i]['brand'] = "Yale";
        }
        $lockDevList[$i]['uuid'] = $lockInfo['UUID'];
        $lockDevList[$i]['status'] = intval($lockInfo['LockStatus']);
        $lockDevList[$i]['door_status'] = intval($lockInfo['DoorStatus']);
        $lockDevList[$i]['location'] = $lockInfo['LockName'];

        $bondedDevices = array();
        $bondedDevices['mac'] = $lockInfo['MAC'];
        if ($lockInfo['Relay']) {
            $bondedDevices['relay_id'] = log($lockInfo['Relay'], 2);
        } else {
            $bondedDevices['relay_id'] = -1;
        }

        $lockDevList[$i]['bonded_devices'] = $bondedDevices;

        $i++;
    }

    //bsi蓝牙锁列表
    $sth = $db->prepare("select UUID,LockName,LockID,LockPwd,LockMac from BSILockDevice where PersonalAccountUUID = :uuid");
    $sth->bindParam(':uuid', $resultToken['NodeUUID'], PDO::PARAM_STR);
    $sth->execute();
    $bsiLocks = $sth->fetchALL(PDO::FETCH_ASSOC);

    $bsiLockList = array();
    $i = 0;
    foreach ($bsiLocks as $lockInfo) {
        $bsiLockList[$i]['uuid'] = $lockInfo['UUID'];
        $bsiLockList[$i]['location'] = $lockInfo['LockName'];
        $bsiLockList[$i]['ble_id'] = $lockInfo['LockID'];
        $bsiLockList[$i]['ble_pwd'] = $lockInfo['LockPwd'];
        $bsiLockList[$i]['mac'] = $lockInfo['LockMac'];
        $bsiLockList[$i]['brand'] = 'BSI';
        $bsiLockList[$i]['type'] = BSI_LOCK_TYPE;
        $i++;
    }

    $thirdPartyDevList['lock_dev_list'] = $lockDevList;
    $thirdPartyDevList['bsi_lock_list'] = $bsiLockList;
}

function bindAuthdata($personalAccountUuid, &$authData, $lockType)
{
    $db = \util\container\getDB();
    //绑定时如果该品牌已绑过账号，清空后再添加
    $sth = $db->prepare("SELECT ID FROM ThirdPartyLockAccount where PersonalAccountUUID = :PersonalAccountUUID and LockType = :lock_type");
    $sth->bindParam(':PersonalAccountUUID', $personalAccountUuid, PDO::PARAM_STR);
    $sth->bindParam(':lock_type', $lockType, PDO::PARAM_INT);
    $sth->execute();
    $authInfo = $sth->fetch(PDO::FETCH_ASSOC);
    if ($authInfo) {
        $sth = $db->prepare("DELETE FROM ThirdPartyLockAccount where PersonalAccountUUID = :PersonalAccountUUID and LockType = :lock_type");
        $sth->bindParam(':PersonalAccountUUID', $personalAccountUuid, PDO::PARAM_STR);
        $sth->bindParam(':lock_type', $lockType, PDO::PARAM_INT);
        $sth->execute();
        $sth = $db->prepare("DELETE FROM ThirdPartyLockDevice where PersonalAccountUUID = :PersonalAccountUUID and LockType = :lock_type");
        $sth->bindParam(':PersonalAccountUUID', $personalAccountUuid, PDO::PARAM_STR);
        $sth->bindParam(':lock_type', $lockType, PDO::PARAM_INT);
        $sth->execute();
    }

    $uuid = \util\common\getMysqlUUID();
    $sth = $db->prepare("INSERT INTO ThirdPartyLockAccount (UUID,Token,RefreshToken,ExpireTime,PersonalAccountUUID,LockType) VALUES (:uuid,:token,:refresh_token,:expire_time,:PersonalUUID,:lock_type)");
    $sth->bindParam(':uuid', $uuid, PDO::PARAM_STR);
    $sth->bindParam(':PersonalUUID', $personalAccountUuid, PDO::PARAM_STR);
    $sth->bindParam(':lock_type', $lockType, PDO::PARAM_INT);
    $sth->bindParam(':token', $authData['Token'], PDO::PARAM_STR);
    $sth->bindParam(':refresh_token', $authData['RefreshToken'], PDO::PARAM_STR);
    $sth->bindParam(':expire_time', $authData['ExpireTime'], PDO::PARAM_STR);
    $sth->execute();
}

function updateThirdPartyDevlist($userConf, $data, $thirdUuidArr, $type, $token = '')
{
    $db = \util\container\getDB();
    $sth = $db->prepare("SELECT DeviceUUID,UUID FROM ThirdPartyLockDevice where PersonalAccountUUID = :PersonalUUID and LockType = :lock_type");
    $sth->bindParam(':PersonalUUID', $userConf['NodeUUID'], PDO::PARAM_STR);
    $sth->bindParam(':lock_type', $type, PDO::PARAM_INT);
    $sth->execute();
    $lockUuidArr = array();
    $uuidArr = array();
    $lockUuidInfo = $sth->fetchAll(PDO::FETCH_ASSOC);
    $i = 0;
    foreach ($lockUuidInfo as $lockUuid) {
        $lockUuidArr[$i] = $lockUuid['DeviceUUID'];
        $uuidArr[$i] = $lockUuid['UUID'];
        $i++;
    }

    //在数据库但是不在第三方获取的锁列表中，需要删除
    $deleteArr = array_diff($lockUuidArr, $thirdUuidArr);
    if ($deleteArr != null) {
        foreach ($deleteArr as $key => $value) {
            $sth = $db->prepare("DELETE FROM ThirdPartyLockDevice where UUID = :uuid");
            $sth->bindParam(':uuid', $uuidArr[$key], PDO::PARAM_STR);
            $sth->execute();
        }
    }
    //在第三方锁列表中但是不在数据库中，需要新增
    $insertArr = array_diff($thirdUuidArr, $lockUuidArr);
    if ($insertArr != null) {
        foreach ($insertArr as $key => $value) {
            if (!array_key_exists('DoorStatus', $data[$key])) {
                $doorStatus = THIRDPARTY_DOOR_STATUS_UNKNOWN;
            } else {
                $doorStatus = $data[$key]['DoorStatus'];
            }
            $uuid = \util\common\getMysqlUUID();
            $sth = $db->prepare("INSERT INTO ThirdPartyLockDevice (UUID,PersonalAccountUUID,LockType,DeviceUUID,LockName,LockStatus,DoorStatus, IsYaleL2Lock) VALUES (:uuid,:personal_uuid,:lock_type,:dev_uuid,:lock_name,:lock_status,:door_status, :is_yale_l2_lock)");
            $sth->bindParam(':uuid', $uuid, PDO::PARAM_STR);
            $sth->bindParam(':personal_uuid', $userConf['NodeUUID'], PDO::PARAM_STR);
            $sth->bindParam(':lock_type', $type, PDO::PARAM_INT);
            $sth->bindParam(':dev_uuid', $data[$key]['DeviceUUID'], PDO::PARAM_STR);
            $sth->bindParam(':lock_name', $data[$key]['LockName'], PDO::PARAM_STR);
            $sth->bindParam(':lock_status', $data[$key]['LockStatus'], PDO::PARAM_INT);
            $sth->bindParam(':door_status', $doorStatus, PDO::PARAM_INT);
            $sth->bindParam(':is_yale_l2_lock', $data[$key]['IsYaleL2Lock'], PDO::PARAM_INT);
            $sth->execute();
            if ($type == YALE_LOCK_TYPE) {
                addYaleWebhook($data[$key]['DeviceUUID'], $token, md5($uuid));
            }
        }
    }
    //两者交集，需要更新状态
    $updateArr = array_intersect($thirdUuidArr, $lockUuidArr);
    if ($updateArr != null) {
        foreach ($updateArr as $key => $value) {
            if ($data[$key]) {
                if (!array_key_exists('DoorStatus', $data[$key])) {
                    $doorStatus = THIRDPARTY_DOOR_STATUS_UNKNOWN;
                } else {
                    $doorStatus = $data[$key]['DoorStatus'];
                }
                $sth = $db->prepare("UPDATE ThirdPartyLockDevice SET LockStatus = :lock_status, DoorStatus = :door_status, IsYaleL2Lock = :is_yale_l2_lock  where DeviceUUID = :dev_uuid");
                $sth->bindParam(':lock_status', $data[$key]['LockStatus'], PDO::PARAM_INT);
                $sth->bindParam(':dev_uuid', $data[$key]['DeviceUUID'], PDO::PARAM_STR);
                $sth->bindParam(':door_status', $doorStatus, PDO::PARAM_INT);
                $sth->bindParam(':is_yale_l2_lock', $data[$key]['IsYaleL2Lock'], PDO::PARAM_INT);
                $sth->execute();
            }
        }
    }
}

function addThirdPartyLockDevice($userConf, $devUuid, $uuid, $lockType, $lockName, $lockStatus, $token = "", $doorStatus = THIRDPARTY_DOOR_STATUS_UNKNOWN)
{
    $db = \util\container\getDB();
    $sth = $db->prepare("SELECT ID FROM ThirdPartyLockDevice where PersonalAccountUUID = :PersonalAccountUUID and DeviceUUID = :dev_uuid");
    $sth->bindParam(':PersonalAccountUUID', $userConf['NodeUUID'], PDO::PARAM_STR);
    $sth->bindParam(':dev_uuid', $devUuid, PDO::PARAM_STR);
    $sth->execute();
    $lockinfo = $sth->fetch(PDO::FETCH_ASSOC);
    if (!$lockinfo) {
        $sth1 = $db->prepare("INSERT INTO ThirdPartyLockDevice (UUID,LockType,DeviceUUID,LockName,LockStatus,PersonalAccountUUID,DoorStatus) VALUES (:UUID,:LockType,:DeviceUUID,:LockName,:LockStatus,:PersonalAccountUUID,:DoorStatus)");
        $sth1->bindParam(':UUID', $uuid, PDO::PARAM_STR);
        $sth1->bindParam(':LockType', $lockType, PDO::PARAM_INT);
        $sth1->bindParam(':DeviceUUID', $devUuid, PDO::PARAM_STR);
        $sth1->bindParam(':LockName', $lockName, PDO::PARAM_STR);
        $sth1->bindParam(':LockStatus', $lockStatus, PDO::PARAM_INT);
        $sth1->bindParam(':PersonalAccountUUID', $userConf['NodeUUID'], PDO::PARAM_STR);
        $sth1->bindParam(':DoorStatus', $doorStatus, PDO::PARAM_INT);
        $sth1->execute();
        if ($lockType == YALE_LOCK_TYPE) {
            addYaleWebhook($devUuid, $token, md5($uuid));
        }
    }
}

function getYaleLockInfoByDeviceUUID($deviceUUID)
{
    $db = \util\container\getDB();
    $sth = $db->prepare("SELECT UUID, PersonalAccountUUID, LockName  FROM ThirdPartyLockDevice where DeviceUUID = :lockid and LockType = ".YALE_LOCK_TYPE." order by id desc limit 1");
    $sth->bindParam(':lockid', $deviceUUID, PDO::PARAM_STR);
    $sth->execute();
    $data = $sth->fetch(PDO::FETCH_ASSOC);
    if ($data) {
        $sth = $db->prepare("SELECT Token FROM ThirdPartyLockAccount where PersonalAccountUUID = :uid and LockType = ".YALE_LOCK_TYPE." order by id desc limit 1");
        $sth->bindParam(':uid', $data['PersonalAccountUUID'], PDO::PARAM_STR);
        $sth->execute();
        $ret = $sth->fetch(PDO::FETCH_ASSOC);
        if ($ret) {
            $data['Token'] = $ret['Token'];
        }
    }
    return $data;
}

function updateDoorStatus($deviceUUID, $doorStatus)
{
    $db = \util\container\getDB();
    $sth = $db->prepare("UPDATE ThirdPartyLockDevice SET DoorStatus = :status where UUID = :uuid");
    $sth->bindParam(':uuid', $deviceUUID, PDO::PARAM_STR);
    $sth->bindParam(':status', $doorStatus, PDO::PARAM_INT);
    $sth->execute();
}

function addYaleWebhook($lockID, $token, $authKey)
{
    $data['url'] = REST_SERVER_URL.'/yale/webhook';
    $data['clientID'] = YALE_CLIENT_ID;
    $data['header'] = 'x-auth-token';
    $data['token'] = $authKey;
    $data['method'] = 'POST';
    $data['notificationTypes'] = ['operation','battery'];
    $headers=['content-type:application/json', 'x-august-api-key:'.YALE_API_KEY, 'x-august-access-token:'.$token];
    $output = \util\common\httpRequest("post", YALE_API_URL.'/webhook/'.$lockID, $headers, json_encode($data), 1);
    if ($output) {
        $outputArr = json_decode($output, true);
        if ($outputArr['message'] == 'success') {
            return true;
        } else {
            \util\log\akcsLog::debug("addYaleWebhook error:" . $output);
        }
    }
    return false;
}

function addBSILock($bleId, $blePwd, $mac, $userConf)
{
    //app生成ble_id时会根据已存在的id生成不同的ble_id，所以同一个用户下不存在相同id的锁
    $db = \util\container\getDB();
    $uuid = \util\common\getMysqlUUID();
    $sth = $db->prepare("SELECT ID FROM BSILockDevice where PersonalAccountUUID = :PersonalAccountUUID and LockID = :lock_id");
    $sth->bindParam(':PersonalAccountUUID', $userConf['NodeUUID'], PDO::PARAM_STR);
    $sth->bindParam(':lock_id', $bleId, PDO::PARAM_STR);
    $sth->execute();
    $lockinfo = $sth->fetch(PDO::FETCH_ASSOC);
    if (!$lockinfo) {
        $sth1 = $db->prepare("INSERT INTO BSILockDevice (UUID,LockID,LockPwd,PersonalAccountUUID,LockName,LockMac) VALUES (:uuid,:lock_id,:lock_pwd,:personal_uuid,'BSI',:lock_mac)");
        $sth1->bindParam(':uuid', $uuid, PDO::PARAM_STR);
        $sth1->bindParam(':lock_id', $bleId, PDO::PARAM_INT);
        $sth1->bindParam(':lock_pwd', $blePwd, PDO::PARAM_STR);
        $sth1->bindParam(':personal_uuid', $userConf['NodeUUID'], PDO::PARAM_STR);
        $sth1->bindParam(':lock_mac', $mac, PDO::PARAM_STR);
        $sth1->execute();
    }
}