<?php

require_once(dirname(__FILE__) . '/notify.php');
require_once(dirname(__FILE__) . '/../config/define.php');
require_once(dirname(__FILE__) . '/proto/proto.php');

define("UNIX_DOMAIN", "/var/adapt_sock/adapt.sock");

class Byte
{

    //长度
    private $length = 0;
    private $byte = '';
    //操作码
    private $code;

    public function setBytePrev($content)
    {
        $this->byte = $content . $this->byte;
    }

    public function getByte()
    {
        return $this->byte;
    }

    public function getLength()
    {
        return $this->length;
    }

    public function writeChar($string, $size)
    {
        $this->byte .= pack('a' . $size, $string);
        $this->length += $size;
        return; //下面为V3.2使用的

        $strsize = strlen($string);
        if ($size <= $strsize) {
            exit('字符串超长');
        }
        $this->length += $strsize;
        $str = array_map('ord', str_split($string));
        foreach ($str as $vo) {
            $this->byte .= pack('c', $vo);
        }

        for ($i = 1; $i <= ($size - $strsize); $i++) {
            $this->byte .= pack('c', '0');
            $this->length++;
        }
    }

    public function writeInt($str)
    {
        $this->length += 4;
        $this->byte .= pack('N', $str);
    }

    public function writeShortInt($interge)
    {
        $this->length += 2;
        $this->byte .= pack('N', $interge);
    }

    public function writeProtobuf($string)
    {
        $this->byte = $string;
        $strsize = strlen($string);
        $this->length += $strsize;
    }
}

/* 4.6注释，以下Class到CSocket前均已不使用 */
/* --------------------------------------------------------------------------- */

/* * 界面增加设备时php将设备信息传给csadpt,php<-->csmain
 *
 * @param[IN]  szMac -- 相关设备的MAC地址
 * @param[IN]  szMacOld -- 修改之前设备的MAC地址,只在修改设备时用
 * @param[OUT] ret -- csmain模块返回的错误码，具体见:CS_COMM_ERR_CODE

 * typedef struct CSP2A_DEVICE_INFO_T {
 * 	CHAR szMac[MAC_SIZE];
 *  CHAR szMacOld[MAC_SIZE];
 *   INT  nRet;
 * }CSP2A_DEVICE_INFO;
 */

class CAddDevSocket
{
    private $socket;
    private $port = 8503;
    private $host = '*************';
    private $byte;
    //以下为消息头字段定义
    private $id = 0x00400001;
    private $from = 0101;
    private $param1 = 0101;
    private $param2 = 0101;

    //const ID_LENGTH=4;
    const ID_LENGTH = 4;
    const FROM_LENGTH = 4;
    const PARAM1_LENGTH = 4;
    const PARAM2_LENGTH = 4;

    public function __set($name, $value)
    {
        $this->$name = $value;
    }

    public function __construct($host = '*************', $port = 8503)
    {
        $this->host = 'localhost';
        $this->port = 8503;
        $this->socket = socket_create(AF_INET, SOCK_DGRAM, SOL_UDP);
        if (!$this->socket) {
            exit('Create socket failed');
        }
        //$result = socket_connect($this->socket,$this->host,$this->port);
        //if(!$result){
        //	exit('Connect host failed:'.$this->host);
        //}
        $this->byte = new Byte();
    }

    public function copyDev($data)
    {
        if (is_string($data) || is_int($data) || is_float($data)) {
            $data[] = $data;
        }
        if (is_array($data)) {

            //严格按照结构体字段顺序进行赋值
            $this->byte->writeChar($data[0], 20);
            $this->byte->writeChar($data[1], 20);
            $this->byte->writeInt($data[2]);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }

    public function recvMsg()
    {
        $result = socket_recvfrom($this->socket, $buf, strlen($this->byte->getByte()), 0, $this->host, $this->port);
        if ($result == -1) {
            exit('recvMsg failed');
        }
        return $buf;
    }

    /*
     * 构造消息头
     * 消息头=length+id+from+param1+param2
     * length是总长度(4字节),id消息类型(4字节),from暂时无用(4字节),param1/param2(预留字段,均为4字节)
     */

    private function getHeader()
    {
        $length = $this->byte->getLength();
        $length = intval($length) + self::ID_LENGTH + self::FROM_LENGTH + self::PARAM1_LENGTH + self::PARAM2_LENGTH;
        return pack('N', $length);
    }

    private function getMsgId()
    {
        return pack("N", 0x00400001);
    }

    private function getMsgFrom()
    {
        return pack('N', $this->from);
    }

    private function getMsgParam1()
    {
        return pack('N', $this->param1);
    }

    private function getMsgParam2()
    {
        return pack('N', $this->param2);
    }

    //构造消息头
    private function setMsgHead()
    {
        $this->byte->setBytePrev($this->getHeader() . $this->getMsgId() . $this->getMsgFrom() . $this->getMsgParam1() . $this->getMsgParam2());
    }

    private function sendMsg()
    {
        $result = socket_sendto($this->socket, $this->byte->getByte(), strlen($this->byte->getByte()), 0, $this->host, $this->port);
        if (!$result) {
            exit('sendMsg failed');
        }
    }

    public function __desctruct()
    {
        socket_close($this->socket);
    }
}

/* * 界面修改设备时php将设备信息传给csadpt,php<-->csmain
 *
 * @param[IN]  szMac -- 相关设备的MAC地址
 * @param[IN]  szMacOld -- 修改之前设备的MAC地址,只在修改设备时用
 * @param[OUT] ret -- csmain模块返回的错误码，具体见:CS_COMM_ERR_CODE

 * typedef struct CSP2A_DEVICE_INFO_T {
 * 	CHAR szMac[MAC_SIZE];
 *  CHAR szMacOld[MAC_SIZE];
 *   INT  nRet;
 * }CSP2A_DEVICE_INFO;
 */

class CModifyDevSocket
{
    private $socket;
    private $port = 8503;
    private $host = '*************';
    private $byte;
    //以下为消息头字段定义
    private $id = 0x00400002;
    private $from = 0101;
    private $param1 = 0101;
    private $param2 = 0101;

    //const ID_LENGTH=4;
    const ID_LENGTH = 4;
    const FROM_LENGTH = 4;
    const PARAM1_LENGTH = 4;
    const PARAM2_LENGTH = 4;

    public function __set($name, $value)
    {
        $this->$name = $value;
    }

    public function __construct()
    {
        $this->host = 'localhost';
        $this->port = 8503;
        $this->socket = socket_create(AF_INET, SOCK_DGRAM, SOL_UDP);
        if (!$this->socket) {
            exit('Create socket failed');
        }
        //$result = socket_connect($this->socket,$this->host,$this->port);
        //if(!$result){
        //	exit('Connect host failed:'.$this->host);
        //}
        $this->byte = new Byte();
    }

    public function copyDev($data)
    {
        if (is_string($data) || is_int($data) || is_float($data)) {
            $data[] = $data;
        }
        if (is_array($data)) {

            //严格按照结构体字段顺序进行赋值
            $this->byte->writeChar($data[0], 20);
            $this->byte->writeChar($data[1], 20);
            $this->byte->writeInt($data[2]);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }

    public function recvMsg()
    {
        $result = socket_recvfrom($this->socket, $buf, strlen($this->byte->getByte()), 0, $this->host, $this->port);
        if ($result == -1) {
            exit('recvMsg failed');
        }
        return $buf;
    }

    /*
     * 构造消息头
     * 消息头=length+id+from+param1+param2
     * length是总长度(4字节),id消息类型(4字节),from暂时无用(4字节),param1/param2(预留字段,均为4字节)
     */

    private function getHeader()
    {
        $length = $this->byte->getLength();
        $length = intval($length) + self::ID_LENGTH + self::FROM_LENGTH + self::PARAM1_LENGTH + self::PARAM2_LENGTH;
        return pack('N', $length);
    }

    private function getMsgId()
    {
        return pack("N", 0x00400002);
    }

    private function getMsgFrom()
    {
        return pack('N', $this->from);
    }

    private function getMsgParam1()
    {
        return pack('N', $this->param1);
    }

    private function getMsgParam2()
    {
        return pack('N', $this->param2);
    }

    //构造消息头
    private function setMsgHead()
    {
        $this->byte->setBytePrev($this->getHeader() . $this->getMsgId() . $this->getMsgFrom() . $this->getMsgParam1() . $this->getMsgParam2());
    }

    private function sendMsg()
    {
        $result = socket_sendto($this->socket, $this->byte->getByte(), strlen($this->byte->getByte()), 0, $this->host, $this->port);
        if (!$result) {
            exit('sendMsg failed');
        }
    }

    public function __desctruct()
    {
        socket_close($this->socket);
    }
}

/* * 界面需要远程重启设备时php将设备信息传给csadpt,php<-->csmain
 *
 * @param[IN]  szMac -- 相关设备的MAC地址
 * @param[OUT] ret -- csmain模块返回的错误码，具体见:CS_COMM_ERR_CODE

 * typedef struct CSP2A_REBOOT_DEVICE_T{
 *   CHAR szMac[MAC_SIZE];
 *   INT  nRet;
 * }CSP2A_REBOOT_DEVICE;
 */

class CRebootDevSocket
{
    private $socket;
    private $port = 8503;
    private $host = '*************';
    private $byte;
    //以下为消息头字段定义
    private $id = 0x00400004;
    private $from = 0101;
    private $param1 = 0101;
    private $param2 = 0101;

    //const ID_LENGTH=4;
    const ID_LENGTH = 4;
    const FROM_LENGTH = 4;
    const PARAM1_LENGTH = 4;
    const PARAM2_LENGTH = 4;

    public function __set($name, $value)
    {
        $this->$name = $value;
    }

    public function __construct()
    {
        $this->host = 'localhost';
        $this->port = 8503;
        $this->socket = socket_create(AF_INET, SOCK_DGRAM, SOL_UDP);
        if (!$this->socket) {
            exit('Create socket failed');
        }
        //$result = socket_connect($this->socket,$this->host,$this->port);
        //if(!$result){
        //	exit('Connect host failed:'.$this->host);
        //}
        $this->byte = new Byte();
    }

    public function copyDev($data)
    {
        if (is_string($data) || is_int($data) || is_float($data)) {
            $data[] = $data;
        }
        if (is_array($data)) {

            //严格按照结构体字段顺序进行赋值
            $this->byte->writeChar($data[0], 20);
            $this->byte->writeInt($data[1]);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }

    public function recvMsg()
    {
        $result = socket_recvfrom($this->socket, $buf, strlen($this->byte->getByte()), 0, $this->host, $this->port);
        if ($result == -1) {
            exit('recvMsg failed');
        }
        return $buf;
    }

    /*
     * 构造消息头
     * 消息头=length+id+from+param1+param2
     * length是总长度(4字节),id消息类型(4字节),from暂时无用(4字节),param1/param2(预留字段,均为4字节)
     */

    private function getHeader()
    {
        $length = $this->byte->getLength();
        $length = intval($length) + self::ID_LENGTH + self::FROM_LENGTH + self::PARAM1_LENGTH + self::PARAM2_LENGTH;
        return pack('N', $length);
    }

    private function getMsgId()
    {
        return pack("N", 0x00400004);
    }

    private function getMsgFrom()
    {
        return pack('N', $this->from);
    }

    private function getMsgParam1()
    {
        return pack('N', $this->param1);
    }

    private function getMsgParam2()
    {
        return pack('N', $this->param2);
    }

    //构造消息头
    private function setMsgHead()
    {
        $this->byte->setBytePrev($this->getHeader() . $this->getMsgId() . $this->getMsgFrom() . $this->getMsgParam1() . $this->getMsgParam2());
    }

    private function sendMsg()
    {
        $result = socket_sendto($this->socket, $this->byte->getByte(), strlen($this->byte->getByte()), 0, $this->host, $this->port);
        if (!$result) {
            exit('sendMsg failed');
        }
    }

    public function __desctruct()
    {
        socket_close($this->socket);
    }
}


//v4.2
//基类
class CSocket
{
    private $socket;
    private $port = 8503;
    private $host = 'localhost';
    public $byte;
    //以下为消息头字段定义
    private $id;
    private $from = 0101;
    private $param1 = 0101;
    private $param2 = 0101;

    //const ID_LENGTH=4;
    const ID_LENGTH = 4;
    const FROM_LENGTH = 4;
    const PARAM1_LENGTH = 4;
    const PARAM2_LENGTH = 4;

    public function __set($name, $value)
    {
        $this->$name = $value;
    }

    public function __construct()
    {
        $this->host = 'localhost';
        $this->port = 8503;
        $this->socket = socket_create(AF_UNIX, SOCK_STREAM, 0);
        if (!$this->socket) {
            LOG_TRACE('phpsocket: CSocket: Create socket failed');
            return;
        }
        socket_connect($this->socket, UNIX_DOMAIN);
        $this->byte = new Byte();
    }

    public function setMsgID($id)
    {
        $this->id = $id;
        return;
    }
    
    public function setMsgProjectType($from)
    {
        $this->from = $from;
        return;
    }

    public function copy($data)
    { //须由子类重载
    }

    public function recvMsg()
    {
        $buf = socket_read($this->socket, strlen($this->byte->getByte()));
        return $buf;
    }

    /*
     * 构造消息头
     * 消息头=length+id+from+param1+param2
     * length是总长度(4字节),id消息类型(4字节),from暂时无用(4字节),param1/param2(预留字段,均为4字节)
     */

    private function getHeader()
    {
        $length = $this->byte->getLength();
        $length = intval($length) + self::ID_LENGTH + self::FROM_LENGTH + self::PARAM1_LENGTH + self::PARAM2_LENGTH;
        return pack('N', $length);
    }

    private function getMsgId()
    {
        return pack("N", $this->id);
    }

    private function getMsgFrom()
    {
        return pack('N', $this->from);
    }

    private function getMsgParam1()
    {
        return pack('N', $this->param1);
    }

    private function getMsgParam2()
    {
        return pack('N', $this->param2);
    }

    //构造消息头
    public function setMsgHead()
    {
        $this->byte->setBytePrev($this->getHeader() . $this->getMsgId() . $this->getMsgFrom() . $this->getMsgParam1() . $this->getMsgParam2());
    }

    public function sendMsg()
    {
        $result = socket_write($this->socket, $this->byte->getByte(), strlen($this->byte->getByte()));
        if (!$result) {
            LOG_TRACE('phpsocket: CSocket: sendMsg failed--msgid' . $this->id);
            return;
        }
    }

    public function __desctruct()
    {
        socket_close($this->socket);
    }
}

/* * app上删除视频
 */

class CDelVideoNotifySocket extends CSocket
{
    public function copy($data)
    {
        if (is_string($data) || is_int($data) || is_float($data)) {
            $data[] = $data;
        }
        if (is_array($data)) {
            $TempData = new AK\Adapt\NotityDelVideo();
            $TempData->setVideoId($data[0]);
            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}

/* * app开门
 */

class COpenDoorNotifySocket extends CSocket
{
    public function copy($data)
    {
        if (is_string($data) || is_int($data) || is_float($data)) {
            $data[] = $data;
        }
        if (is_array($data)) {
            $TempData = new AK\Adapt\OpenDoorNotify();
            $TempData->setMac($data[0]);
            $TempData->setUid($data[1]);
            $TempData->setRelay($data[2]);
            $TempData->setRepost($data[3]);
            $TempData->setMsgTraceid($data[4]);
            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}

class CWebPersonalModifyNotifySocket extends CSocket
{
    public function copy($data)
    {
        if (is_string($data) || is_int($data) || is_float($data)) {
            $data[] = $data;
        }
        if (is_array($data)) {
            $TempData = new AK\Adapt\WebPersonalModifyNotify();
            $TempData->setChangeType((int) $data[0]);
            $TempData->setNode((string) $data[1]);
            $macs = explode(";", $data[2]);
            $TempData->setMacList($macs);
            $TempData->setInstallerId((int) $data[3]);
            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}

class CWebCommunityModifyNotifySocket extends CSocket
{
    public function copy($data)
    {
        if (is_string($data) || is_int($data) || is_float($data)) {
            $data[] = $data;
        }
        if (is_array($data)) {
            $TempData = new AK\Adapt\WebCommunityModifyNotify();
            $TempData->setChangeType((int) $data[0]);
            $TempData->setNode((string) $data[1]);
            $macs = explode(";", $data[2]);
            $TempData->setMacList($macs);
            $TempData->setCommunityId((int) $data[3]);
            $TempData->setUnitId((int) $data[4]);
            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}

//Deal Alarm相关通知
class CAlarmDealSocket extends CSocket
{
    public function copy($data)
    {
        if (is_string($data)||is_int($data)||is_float($data)) {
            $data[]=$data;
        }
        if (is_array($data)) {
            $TempData = new AK\Adapt\PersonalAlarmDeal();
            $TempData->setAreaNode((string)$data[0]);
            $TempData->setUser((string)$data[1]);
            $TempData->setAlarmId((int)$data[2]);
            $TempData->setResult((string)$data[3]);
            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}

class CSendSmsCodeSocket extends CSocket
{
    public function copy($data)
    {
        if (is_string($data) || is_int($data) || is_float($data)) {
            $data[] = $data;
        }
        if (is_array($data)) {
            $TempData = new AK\Adapt\SendSmsCode();
            $TempData->setType((int) $data[0]);
            $TempData->setCode((string) $data[1]);
            $TempData->setAreaCode((string) $data[2]);
            $TempData->setPhone((string) $data[3]);
            $TempData->setUserType((int) $data[4]);
            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}


class CFeedbackNotifySocket extends CSocket
{
    public function copy($data)
    {
        if (is_string($data) || is_int($data) || is_float($data)) {
            $data[] = $data;
        }
        if (is_array($data)) {
            $TempData = new AK\Adapt\FeedbackNotify();
            $TempData->setDatas((string) $data[0]);
            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}

class CWebCommunityAccountModifyNotifySocket extends CSocket
{
    public function copy($data)
    {
        if (is_string($data) || is_int($data) || is_float($data)) {
            $data[] = $data;
        }
        if (is_array($data)) {
            $TempData = new AK\Adapt\CommunityAccountModifyNotify();

            $TempData->SetAccessGroupIdList($data[0]);
            $TempData->SetCommunityId((int)$data[1]);
            $TempData->SetAccountList($data[2]);
            $TempData->SetNodeList($data[3]);
           
            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}

/* * app开门 开security relay
 */

class COpenSecurityRelayNotifySocket extends CSocket
{
    public function copy($data)
    {
        if (is_string($data) || is_int($data) || is_float($data)) {
            $data[] = $data;
        }
        if (is_array($data)) {
            $TempData = new AK\Adapt\OpenSecurityRelayNotify();
            $TempData->setMac($data[0]);
            $TempData->setUid($data[1]);
            $TempData->setSecurityRelay((int)$data[2]);
            $TempData->setMsgTraceid($data[3]);
            $TempData->setRepost((int)$data[4]);
            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}

class CPerNewMessageSocket extends CSocket
{
	public function copy($data){
		if(is_string($data)||is_int($data)||is_float($data)){
			$data[]=$data;
		}
		if(is_array($data)){
			
			$TempData = new AK\Adapt\PerNewMessage();
			if(strlen($data[0]) != 0)
			{
				$TempData->setData($data[0]);
			}
			$PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
		}
		$this->setMsgHead();
		$this->sendMsg();
        return;
	}
}

class CThirdPartyLockNotifySocket extends CSocket
{
    public function copy($data)
    {
        if (is_string($data) || is_int($data) || is_float($data)) {
            $data[] = $data;
        }
        if (is_array($data)) {
            $TempData = new AK\Adapt\ThirdPartyLockNotify();
            $TempData->setMessagetype($data[0]);
            $TempData->setCaptureType($data[1]);
            $TempData->setLockType($data[2]);
            $TempData->setUuid($data[3]);
            $TempData->setPersonalAccountUuid($data[4]);
            $TempData->setInitiator($data[5]);
            $TempData->setLockName($data[6]);
            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}

class CSendEmailNotifySocket extends CSocket
{
    public function copy($data)
    {
        if (is_string($data)||is_int($data)||is_float($data)) {
            $data[]=$data;
        }
        if (is_array($data)) {
            $TempData = new AK\Adapt\SendEmailNotifyMsg();
            $TempData->setKey($data[0]);
            $TempData->setPayload($data[1]);
            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}

class CSingleContactRuleNotifySocket extends CSocket
{
    public function copy($data)
    {
        if (is_string($data)||is_int($data)||is_float($data)) {
            $data[]=$data;
        }
        if (is_array($data)) {
            $TempData = new AK\Adapt\SingleCallModifyNotify();
            $TempData->setAccount($data[0]);
            $TempData->setUnitId($data[1]);
            $TempData->setMngId($data[2]);
            $PbData = $TempData->serializeToString();
            $this->byte->writeProtobuf($PbData);
        }
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}