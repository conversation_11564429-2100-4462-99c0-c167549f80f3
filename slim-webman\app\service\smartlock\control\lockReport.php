<?php

namespace  smartlock\control;
require_once __DIR__ . "/../../smartlock/model/smartLock.php";
require_once __DIR__ . "/../../../util/common.php";

#KIT_CONFIG
const KIT_CLIENT_ID = "akcs-csmain-client";
const KIT_CLIENT_SECRET = "8DkVZSK0N81fsZkle1lhc1eHibsCWJpUGRGAec9F8rSYIsW0yc8tSO080odJiHst";
const KIT_CLIENT_ID_SECRET = KIT_CLIENT_ID ."_". KIT_CLIENT_SECRET;

// 处理接口返回的结果
// 数据示例  {"code":0,"msg":"success","data":{"code":"13041119","lockUuid":"xxxxxx"}}
function notifyWebAddLock($postDatas, &$lock_uuid)
{
    $isJson = true;
    $header = array();
    $header[] = "Authorization:" . base64_encode(KIT_CLIENT_ID_SECRET);
    $header[] = "Content-Type:application/json";

    $data = array();
    $data['master'] = $postDatas['residence_id'];
    $data['mac'] = $postDatas['device_mac'];
    $data['lockType'] =  strtolower($postDatas['device_style']);
    $web_url = "http://" . WEB_ADAPT_ENTRY . '/appbackend/common/smartLock/create';

    $raw_data = json_encode($data);
    $response = \util\common\httpRequest(
        "post",
        $web_url,
        $header,
        $raw_data,
        $isJson,
        10
    );
    $json_data = json_decode($response, true);
    
    \util\log\akcsLog::debug("request web:$$web_url data: $raw_data response:". $response);

    $code = $json_data['code'];
    if ($code != 0 || !isset($json_data['data']) ) {
        if ($code == 11221032) {
            \util\log\akcsLog::debug("添加超时：app上没有进行添加或者超过等待时间，错误码：{$code}");
        } else if ($code == 11221011) {
            \util\log\akcsLog::debug("被其他家庭添加，错误码：{$code}");
        } else if ($code == 11221012) {
            \util\log\akcsLog::debug("被自己家庭添加，错误码：{$code}");
        } else {
            \util\log\akcsLog::debug("parse response failed: 错误码：{$code}");
        }
        return false;
    }
    
    $lock_uuid = $json_data['data']["lockUuid"];
    return true;
}

function returnUnknownLock()
{
    $response_data = [
       'success' => false,
       'timestamp' => time(),
       'result' => array(),
    ];
    return json($response_data);
}
function lockReport($request, $response)
{
    $json = $request->rawBody();
    $json = rtrim($json, "\0"); //锁上报上来后面多了\0导致json解析失败
    $postDatas = json_decode($json, true);

    $device_style = $postDatas['device_style'];
    if ($device_style == SMARTLOCK_MQTT_CLIENT_FLAG_SL50)
    {
        $lock_uuid = "";
        $ret = notifyWebAddLock($postDatas, $lock_uuid);
        if (strlen($lock_uuid) == 0 || $ret != true)
        {
            return returnUnknownLock();
        }

        $lockInfo = \smartlock\model\getLockInfo($lock_uuid);

        $message = array();
        $message["username"] = SMARTLOCK_MQTT_CLIENT_FLAG_SL50 . "-" . $lockInfo["MAC"];
        $message["password"] = \util\utility\passwdDecode($lockInfo["MqttPwd"]);;
        $message["client_id"] = \util\smartlockAdapt\realClientToMqttClient($lockInfo["UUID"]);
        $message["device_id"] = $lockInfo["UUID"];
        $message["area"] = SERVER_LOCATION;
        $message["port"] = MQTT_TLS_PORT;
        $message["domain"] = MQTT_DOMAIN;
        $message["gate_https_srv"] = SMARTLOCK_HTTP_GATE_SERVER;
        $message["file_https_srv"] = FILE_SERVER;

        $response_data = [
            'success' => true,
            'timestamp' => time(),
            'result' => $message,
        ];
        return json($response_data);
    }
    return returnUnknownLock();
}