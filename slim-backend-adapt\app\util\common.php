<?php
namespace util\common;
require_once __DIR__ . "/../config/define.php";

use PDO;

//特殊请求列表
const SPECIAL_REQ_LIST = ["/", "/welcome", "/office/welcome"];


function httpRequest($method, $url, $headers, $data = '', $isJson = 0, $timeout = 10)
{
    $curl = curl_init();
    curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($curl, CURLOPT_HEADER, false);
    curl_setopt($curl, CURLOPT_TIMEOUT, $timeout);
    curl_setopt($curl, CURLOPT_URL, $url);
    curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
    if ($method == 'post') {
        curl_setopt($curl, CURLOPT_POST, true);

        if ($isJson) {
            curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
        } else {
            curl_setopt($curl, CURLOPT_POSTFIELDS, http_build_query($data));
        }
    } elseif ($method == 'put') {
        curl_setopt($curl, CURLOPT_CUSTOMREQUEST, "PUT");
        if ($isJson) {
            curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
        } else {
            curl_setopt($curl, CURLOPT_POSTFIELDS, http_build_query($data));
        }
    } elseif ($method == 'delete') {
        curl_setopt($curl, CURLOPT_CUSTOMREQUEST, "DELETE");
    }
        
    
    $output = curl_exec($curl);
    if ($output == false) {
        \util\log\akcsLog::debug("Error: ".curl_error($curl));
    }
    curl_close($curl);
    return $output;
}