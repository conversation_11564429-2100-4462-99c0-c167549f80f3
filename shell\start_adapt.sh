#!/bin/bash
# ****************************************************************************
# Author        :   buhuai.su
# Last modified :   2022-08-05
# Filename      :   install.sh
# Version       :
# Description   :   start.sh 启动脚本
# Modifier      :
# ****************************************************************************

set -euo pipefail

export ETCDCTL_API=3

RSYNC_PATH=$1              #代码同步到的目录
PROJECT_RUN_PATH=$2        #项目运行路径
MIDDLEWARE=$3
ENV=$4
HOST=$5
HOSTNAME=$9

# Input:
#   $1: item
#   $2: conf_file
# Output:
#   value: the value of item
grep_conf() {
    grep -w "$1" "$2" | cut -d'=' -f 2 | awk '{gsub(/^[ \t]+|[ \t]+$/, "");print}'
}


create_php_config() {
    PHP_CONFIG=$1

    OFFLINE_TEMP_KEY='const SUPPORT_OFFLINE_TEMP_KEY_COMMUNITYS = array(0);'
    # 1=ccloud/2=scloud/3=ecloud/4=ucloud/5=other
    # 区分国内还是国外环境
    if [ "$SYSTEM_AREA" -eq 1 ];then
        SERVER_TYPE=cn
    elif [ "$SYSTEM_AREA" -eq 2 ];then
        SERVER_TYPE=as
    elif [ "$SYSTEM_AREA" -eq 3 ];then
        SERVER_TYPE=eu
    elif [ "$SYSTEM_AREA" -eq 4 ];then
        SERVER_TYPE=na
    elif [ "$SYSTEM_AREA" -eq 6 ];then
        SERVER_TYPE=ru
    elif [ "$SYSTEM_AREA" -eq 7 ];then
        SERVER_TYPE=jp
    elif [ "$SYSTEM_AREA" -eq 8 ];then
        SERVER_TYPE=au      
    else #5是others
        SERVER_TYPE=ot
    fi

cat > $PHP_CONFIG <<-EOF
<?php

const SERVER_LOCATION = "$SERVER_TYPE";
#db
const AKCS_DATABASEIP="$AKCS_DATABASEIP";
const AKCS_DATABASEPORT = $AKCS_DATABASEPORT;
const LOG_DATABASEIP="$LOG_DATABASEIP";
const LOG_DATABASEPORT = $LOG_DATABASEPORT;
const DATABASEPWD = "$DECRYPTED_DB_PASSWORD";

#redis
const REDISIP = "${REDIS_INNER_IP}";
const ENABLE_REDIS_SENTINEL = "$ENABLE_REDIS_SENTINEL";
const REDIS_SENTINEL_HOSTS = "$SENTINEL_HOSTS";

//kafka集群服务器地址 多个以,分隔
const KAFKA_BROKER_LIST = '${KAFKA_INNER_IP}';
const MQTT_INNER_API_HOST = '${MQTT_INNER_API_HOST}';

EOF
}


PKG_ROOT=$RSYNC_PATH

IP_FILE=/etc/ip
INSTALL_CONF=$PKG_ROOT/web_backend_install.conf
KDC_CONF=/etc/kdc.conf
APP_BACKEND_CONF=$PKG_ROOT/app_backend_install.conf
SVR_CONF=/etc/app_backend_install.conf
HTML_ROOT=/var/www/html
PHP_CONFIG=dynamic_config.php

# 检查 KDC_CONF 文件是否存在
if [ ! -f "$KDC_CONF" ]; then
    echo "文件不存在： $KDC_CONF 不存在."
    exit 1
fi

# ----------------------------------------------------------------------------
# 开始安装
# ----------------------------------------------------------------------------
echo "Begin to install smartHome2"


echo '读取配置'

if [ ! -f $IP_FILE ]; then
    echo "文件不存在：$IP_FILE"
    exit 1
fi

if [ ! -f $INSTALL_CONF ]; then
    echo "文件不存在：$INSTALL_CONF"
    exit 1
fi

# 读取配置
SYSTEM_AREA=$(grep_conf 'SYSTEM_AREA' $INSTALL_CONF)

REDIS_INNER_IP=$(grep_conf 'REDIS_INNER_IP' $INSTALL_CONF)
ENABLE_REDIS_SENTINEL=$(grep_conf 'ENABLE_REDIS_SENTINEL' $INSTALL_CONF)
SENTINEL_HOSTS=$(grep_conf 'SENTINEL_HOSTS' $INSTALL_CONF)
ETCD_INNER_IP=$(grep_conf 'ETCD_INNER_IP' $SVR_CONF)
SERVER_INNER_IP=$(grep_conf 'SERVER_INNER_IP' $IP_FILE)
ENABLE_AKCS_DBPROXY=$(grep_conf 'ENABLE_DBPROXY' $INSTALL_CONF)
AKCS_MYSQL_INNER_IP=$(grep_conf 'MYSQL_INNER_IP' $INSTALL_CONF)
AKCS_DBPROXY_INNER_IP=$(grep_conf 'DBPROXY_INNER_IP' $INSTALL_CONF)
KAFKA_INNER_IP=$(grep_conf 'KAFKA_INNER_IP' $INSTALL_CONF)
LOG_MYSQL_INNER_IP=$(grep_conf 'LOG_MYSQL_INNER_IP' $INSTALL_CONF)

AKCS_DATABASEIP="$AKCS_MYSQL_INNER_IP";
AKCS_DATABASEPORT=3306;
LOG_DATABASEIP="$LOG_MYSQL_INNER_IP";
LOG_DATABASEPORT=3306;

#获取数据库密码
ENCRYPTED_DB_PASSWORD=$(grep '^AKCS_DBUSER01=' $KDC_CONF | awk '{print substr($0, index($0, "=") + 1)}')

# 检查 /bin/crypto 是否存在
if [ ! -x "/bin/crypto" ]; then
    echo "加密工具 /bin/crypto 不存在或不可执行."
    exit 1
fi
# 检查 ENCRYPTED_DB_PASSWORD 是否为空
if [ -z "$ENCRYPTED_DB_PASSWORD" ]; then
    echo "错误: 获取到的加密数据库密码为空."
    exit 1
fi
# 解密数据库密码
DECRYPTED_DB_PASSWORD=$(echo "$ENCRYPTED_DB_PASSWORD" | /bin/crypto -d 2>/dev/null)
# 检查解密是否成功
if [ -z "$DECRYPTED_DB_PASSWORD" ]; then
    echo "错误：无法解密数据库密码！"
    exit 1
fi


if [ $ENABLE_AKCS_DBPROXY -eq 1 ];then
    AKCS_DATABASEIP="$AKCS_DBPROXY_INNER_IP";
    AKCS_DATABASEPORT=3308;
fi


PACKAGE_SLIM_WEBMAN_PATH=$PKG_ROOT/slim_backend_adapt

RUN_SLIM_WEBMAN_PATH=/var/www/html/slim_backend_adapt

DOCKER_APP_NAME=slim_backend_adapt

# 开始安装
if [ -z "$MIDDLEWARE" ];then
    echo "请选择需要部署的中间件";
    exit 1
fi

if [ ! -d $PACKAGE_SLIM_WEBMAN_PATH ]; then mkdir -p $PACKAGE_SLIM_WEBMAN_PATH; fi

MQTT_INNER_API_HOST=`/bin/etcdctl --endpoints=$ETCD_INNER_IP get /akconf/mqtt/inner_api_addr/http --prefix`
MQTT_INNER_API_HOST=`echo $MQTT_INNER_API_HOST | awk '{print $2}' | tr -d '\n'`

create_php_config "$PACKAGE_SLIM_WEBMAN_PATH"/dynamic_config.php
mv -f "$PACKAGE_SLIM_WEBMAN_PATH"/dynamic_config.php "$PACKAGE_SLIM_WEBMAN_PATH"/app/config/

#将slim同步到运行路径
rsync -av --delete \
--exclude="shell" \
--exclude="app_backend_install.conf" \
--exclude="web_backend_install.conf" \
--exclude="web_frontend_install.conf" $PACKAGE_SLIM_WEBMAN_PATH/* $RUN_SLIM_WEBMAN_PATH/

if [ `docker ps -a | grep $DOCKER_APP_NAME | wc -l` -gt 0 ];then docker stop $DOCKER_APP_NAME;docker rm $DOCKER_APP_NAME;fi
#拉起容器

docker run -itd -e TZ=Asia/Shanghai --restart=always -p 8078:8787 -p 9405:9405 -v $RUN_SLIM_WEBMAN_PATH:$RUN_SLIM_WEBMAN_PATH -v /var/log/php:/var/log/php -v /var/adapt_sock:/var/adapt_sock -v /usr/local/php/etc/client.conf:/usr/local/out/php/etc/client.conf  -v /etc/oss_install.conf:/etc/oss_install.conf --name $DOCKER_APP_NAME registry.cn-hangzhou.aliyuncs.com/ak_system/web php $RUN_SLIM_WEBMAN_PATH/start.php start        
#监控插件安装
echo "===执行监控安装==="
bash -x $RSYNC_PATH/shell/monitor.sh $RSYNC_PATH $PROJECT_RUN_PATH $ENV $HOST $HOSTNAME

#注册etcd
export ETCDCTL_API=3
SLIM_WEBMAN_ADDR="$SERVER_INNER_IP:8078"
ret=$(/usr/local/bin/php /bin/etcd_cli.php /bin/etcdctl "$ETCD_INNER_IP" put akcs/slim_backend_adapt/inner/${SERVER_INNER_IP} "$SLIM_WEBMAN_ADDR")
if [[ $ret != *ok!* ]]; then
    echo "plz check etcd server is ok."
    exit 1
fi


echo "install complete."

