<?php

namespace common\model;


use PDO;

function getMessagesNumV70($clientType)
{
    $messageNum = 0;
    $userData = \util\container\getUserData();

    // 新办公文本消息数
    $db =  \util\container\getDb();
    $sth = $db->prepare("/*master*/ SELECT COUNT(*) AS num FROM OfficeMessageReceiver R LEFT JOIN OfficeMessage M ON R.MessageUUID = M.UUID WHERE R.PersonalAccountUUID=:UUID AND R.Status = 0 AND R.ClientType = :clientType");
    $sth->bindValue(':UUID', $userData['UUID'], PDO::PARAM_STR);
    $sth->bindValue(':clientType', $clientType, PDO::PARAM_INT);
    $sth->execute();
    $ret = $sth->fetch(PDO::FETCH_ASSOC);
    if ($ret) {
        $messageNum = $ret['num'];
    }

    // 新办公语音留言数
    $sth = $db->prepare("/*master*/ select count(*) as num from PersonalVoiceMsgList WHERE PersonalAccountUUID=:UUID and Status = 0");
    $sth->bindValue(':UUID', $userData['UUID'], PDO::PARAM_STR);
    $sth->execute();

    $result = $sth->fetch(PDO::FETCH_ASSOC);
    if ($result) {
        $messageNum += $result['num'];
    }

    return $messageNum;
}

// 新办公将Message状态置为已读
function setMessageReadV70($clientType)
{
    $userData = \util\container\getUserData();
    
    // 新办公文本消息置为已读
    $db = \util\container\getDb();
    $sth = $db->prepare("update OfficeMessageReceiver R left join OfficeMessage M on R.MessageUUID = M.UUID set R.Status = 1 where R.PersonalAccountUUID=:UUI   D and R.ClientType = :clientType and R.Status = 0 ");
    $sth->bindValue(':UUID', $userData['UUID'], PDO::PARAM_STR);
    $sth->bindValue(':clientType', $clientType, PDO::PARAM_INT);

    if (!$sth->execute()) {
        \util\log\akcsLog::debug("update OfficeMessageReceiver failed");
        return -1;
    }

    // 新办公语音留言置为已读
    $sth = $db->prepare("update PersonalVoiceMsgList set Status = 1 WHERE PersonalAccountUUID=:UUID and Status = 0");
    $sth->bindValue(':UUID', $userData['UUID'], PDO::PARAM_STR);

    if (!$sth->execute()) {
        \util\log\akcsLog::debug("update PersonalVoiceMsgList failed");
        return -1;
    }

    return 0;
}
