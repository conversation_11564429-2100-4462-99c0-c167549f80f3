<?php
namespace app\middleware;

use Webman\MiddlewareInterface;
use Webman\Http\Response;
use Webman\Http\Request;

require_once(__DIR__ . '/../util/common.php');

class AuthMiddle implements MiddlewareInterface
{
    private static $authWhitelist = ['/send_mobile_checkcode',];

    /**
     * 检查URI是否在认证白名单中
     * 
     * @param string $uri 请求URI
     * @return bool 如果在白名单中返回true，否则返回false
     */
    public static function isAuthWhiteList($uri)
    {
        if (in_array($uri, self::$authWhitelist)) {
            return true;
        }

        return false;
    }
    public function process(Request $request, callable $next): Response
    {

        \util\log\akcsLog::request($request);
        \util\log\akcsLog::requestReg($request);

        //鉴权白名单
        $uri = $request->path();
        if (in_array($uri, self::$authWhitelist)) {
            \util\container\setUserData(array());
            $response = $next($request);
            return $response;
        }

        // //校验身份
        // $result = \common\model\checkIdentity();
        // if ($result == ERR_TOKEN_INVALID) {
        //     $response = '';
        //     return \util\response\setResponseMessage($response, ERR_CODE_TOKEN_INVALID);
        // }
        // \util\container\setUserData($result);
        
        $response = $next($request);

        return $response;
    }
}

