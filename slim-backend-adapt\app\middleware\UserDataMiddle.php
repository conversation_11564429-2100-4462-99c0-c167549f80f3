<?php
namespace app\middleware;

use Webman\MiddlewareInterface;
use Webman\Http\Response;
use Webman\Http\Request;

class UserDataMiddle implements MiddlewareInterface
{
    public function process(Request $request, callable $next): Response
    {        
        if(\app\middleware\AuthMiddle::isAuthWhiteList($request->getUri()->getPath()))
        {
            return $next($request);
        }

        // $userData = \util\container\getUserData();
        // $projectType = $userData['ProjectType'];
        // $account = $userData['Account'];

        // if($projectType == PROJECT_TYPE_OFFICE || $projectType == PROJECT_TYPE_NEW_OFFICE) {
        //     $data = \common\model\getOfficeCommInfo($account);
        // }
        // \util\container\setUserData($data);

        $response = $next($request);
        
        return $response;
    }
}

