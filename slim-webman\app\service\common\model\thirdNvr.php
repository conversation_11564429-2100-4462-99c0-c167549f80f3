<?php

namespace common\model;
use PDO;

const DB_THIRD_NVR_TABLE = "ThirdNvr";
const DB_THIRD_NVR_FIELD = ["UUID", "Name", "Type", "UserName", "Password", "Url"];

// 获取位于社区public&楼栋area的所有nvr
function getComPubAllThirdNvrList($projectUUID)
{
    $db =  \util\container\getDb();
    $fieldsString = implode(',', DB_THIRD_NVR_FIELD);
    $sth = $db->prepare("select $fieldsString From ".DB_THIRD_NVR_TABLE." where AccountUUID=:AccountUUID and ProjectType=:ProjectType and PersonalAccountUUID is null;");
    $sth->bindParam(':AccountUUID', $projectUUID, PDO::PARAM_STR);
    $sth->bindValue(':ProjectType', NEW_WEB_PROJECT_TYPE_COMMUNITY, PDO::PARAM_INT);
    $sth->execute();
    $result = $sth->fetchAll(PDO::FETCH_ASSOC);
    return $result;
}

// 获取位于社区Apt下所有可监控的nvr
function getComAptThirdNvrList($projectUUID, $unitUuid, $nodeUuid)
{
    $db =  \util\container\getDb();
    $fieldsString = implode(',', DB_THIRD_NVR_FIELD);
    $sth = $db->prepare("select $fieldsString From ".DB_THIRD_NVR_TABLE." where AccountUUID=:AccountUUID and ProjectType=:ProjectType and IsAllowUserMonitor=1
    and PersonalAccountUUID is null and CommunityUnitUUID is null;");
    $sth->bindParam(':AccountUUID', $projectUUID, PDO::PARAM_STR);
    $sth->bindValue(':ProjectType', NEW_WEB_PROJECT_TYPE_COMMUNITY, PDO::PARAM_INT);
    $sth->execute();
    $pubNvrList = $sth->fetchAll(PDO::FETCH_ASSOC);

    $sth2 = $db->prepare("select $fieldsString From ".DB_THIRD_NVR_TABLE." where AccountUUID=:AccountUUID and ProjectType=:ProjectType and IsAllowUserMonitor=1
    and CommunityUnitUUID =:CommunityUnitUUID and PersonalAccountUUID is null;");
    $sth2->bindParam(':AccountUUID', $projectUUID, PDO::PARAM_STR);
    $sth2->bindValue(':ProjectType', NEW_WEB_PROJECT_TYPE_COMMUNITY, PDO::PARAM_INT);
    $sth2->bindParam(':CommunityUnitUUID', $unitUuid, PDO::PARAM_STR);
    $sth2->execute();
    $unitNvrList = $sth2->fetchAll(PDO::FETCH_ASSOC);

    $sth3 = $db->prepare("select $fieldsString From ".DB_THIRD_NVR_TABLE." where PersonalAccountUUID = :PersonalAccountUUID and ProjectType=:ProjectType;");
    $sth3->bindParam(':PersonalAccountUUID', $nodeUuid, PDO::PARAM_STR);
    $sth3->bindValue(':ProjectType', NEW_WEB_PROJECT_TYPE_COMMUNITY, PDO::PARAM_INT);
    $sth3->execute();
    $personalNvrList = $sth3->fetchAll(PDO::FETCH_ASSOC);

    $result = array_merge($pubNvrList, $unitNvrList, $personalNvrList);
    return $result;
}

// 获取位于单住户Apt下所有可监控的nvr
function getPerAptThirdNvrList($nodeUuid)
{
    $db =  \util\container\getDb();
    $fieldsString = implode(',', DB_THIRD_NVR_FIELD);
    $sth = $db->prepare("select $fieldsString From ".DB_THIRD_NVR_TABLE." where PersonalAccountUUID=:PersonalAccountUUID and ProjectType=:ProjectType and IsAllowUserMonitor=1;");
    $sth->bindParam(':PersonalAccountUUID', $nodeUuid, PDO::PARAM_STR);
    $sth->bindValue(':ProjectType', NEW_WEB_PROJECT_TYPE_SINGLE, PDO::PARAM_INT);
    $sth->execute();
    $result = $sth->fetchAll(PDO::FETCH_ASSOC);
    return $result;
}