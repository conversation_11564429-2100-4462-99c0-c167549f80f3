<?php

namespace  smartlock\control;
require_once __DIR__ . "/../../smartlock/model/smartLock.php";
require_once __DIR__ . "/../../../util/common.php";
require_once __DIR__ . "/../../../util/smartlockAdapt.php";
require_once __DIR__ . "/../../../config/define.php";
require_once __DIR__ . "/../../../config/dynamic_config.php";


/**
 * 检查设备是否在线
 * 
 * @param string $real_clientid 真实的客户端ID
 * @return array 包含设备在线状态和详细信息的数组，失败时返回空数组
 */
function checkDeviceOnlineStatus($real_clientid) 
{
    if (empty($real_clientid)) {
        \util\log\akcsLog::debug("Error: Empty client ID provided");
        return [];
    }
    
    try {
        // API认证信息和URL
        $apiUrl = MQTT_INNER_API_HOST . "/api/v5/clients/" . $real_clientid;
        $headers = [
            'Authorization: Basic ' . base64_encode(MQTT_API_USERNAME . ":" . MQTT_API_PASSWORD)
        ];
        
        // 调用API
        $apiResponse = \util\common\httpRequest('get', $apiUrl, $headers, '', 0, MQTT_API_TIMEOUT);
        
        // 检查API响应
        if ($apiResponse === false) {
            \util\log\akcsLog::debug("MQTT API Request failed for client ID: " . $real_clientid);
            return [];
        }
        
        $apiData = json_decode($apiResponse, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            \util\log\akcsLog::debug("MQTT API Response JSON parse error: " . json_last_error_msg());
            return [];
        }
        
        // 记录API响应
        \util\log\akcsLog::debug("MQTT API Response for " . $real_clientid . ": " . $apiResponse);
        
        return $apiData;
    } catch (\Exception $e) {
        \util\log\akcsLog::debug("Exception in checkDeviceOnlineStatus: " . $e->getMessage());
        return [];
    }
}

function handleDeviceStatusInfo($apiData, $real_clientid) 
{
    // 检查API数据是否为空（API调用失败）
    if (empty($apiData)) {
        \util\log\akcsLog::debug("API data is empty for client: " . $real_clientid);
        return;
    }
    
    // 检查API返回的数据是否包含错误信息
    if (isset($apiData['code'])) {
        if ($apiData['code'] == "CLIENTID_NOT_FOUND") {
            // 客户端未找到，设备离线
            \util\log\akcsLog::debug("Device offline (404): " . $real_clientid);
            \smartlock\model\updateDeviceOffline($real_clientid);
            return;
        } else {
            \util\log\akcsLog::debug("API other error for client: " . $real_clientid . ", code: " . $apiData['code']);
            return;
        }
    }
    
    // 设备在线，返回详细信息
    \util\log\akcsLog::debug("Device online: " . $real_clientid);
    
    $timestamp = strtotime($apiData['connected_at']);
    $onlind_time =  date('Y-m-d H:i:s', $timestamp); 
    $ipport = $apiData['ip_address'] .":".$apiData['port'];
    \smartlock\model\updateDeviceOnline($real_clientid,$ipport,$onlind_time);
    return;
}

function mqttDevicesStatus($request, $response) 
{
    try {
        // 获取请求体
        $data = $request->getParsedBody();
        
        // 获取clientid和event
        $clientid = isset($data['clientid']) ? trim($data['clientid']) : '';
        $event = isset($data['event']) ? trim($data['event']) : '';
    
        \util\log\akcsLog::debug("Parsed clientid: " . $clientid . ", event: " . $event);
        
        // 检查clientid是否以PSL-开头
        if (strpos($clientid, PREMIUM_SMARTLOCK_CLEINT_FLAG) === 0) {
            // 转换clientid
            $real_clientid = \util\smartlockAdapt\mqttClientToRealClient($clientid);
            
            if (!empty($real_clientid)) {
                // 检查设备在线状态
                $apiData = checkDeviceOnlineStatus($real_clientid);
                handleDeviceStatusInfo($apiData, $real_clientid);
            }
        
        }
    } catch (\Exception $e) {
        \util\log\akcsLog::debug("Exception in mqttDevicesStatus: " . $e->getMessage());
        return \util\response\setResponseMessage($response, 500, "Internal server error", [
            'error' => $e->getMessage()
        ]);
    }
    //mqtt不接收返回值
    return \util\response\setResponseMessage($response, 0);
}