<?php

namespace common\model\TTLock;

use PDO;

const DB_TTLOCK_TABLE = 'TtLock';

const DB_TTLOCK_FIELD = [
    'ID',
    'UUID',
    'Name',
    'LockId',
    'DeviceUUID',
    'Relay',
    'PersonalAccountUUID',
    'Version',
    'RBACDataGroupUUID',
    'CreateTime',
    'UpdateTime',
];




// 获取lock信息
function getLockInfo($UUID)
{
    return \util\container\medooDb()->get(
        DB_TTLOCK_TABLE,
        DB_TTLOCK_FIELD,
        ['UUID' => $UUID]
    );
}

// 获取lock信息
function getAptLockList($personalAccountUUID)
{
    return \util\container\medooDb()->select(
        DB_TTLOCK_TABLE,
        DB_TTLOCK_FIELD,
        ['PersonalAccountUUID' => $personalAccountUUID]
    );
}

