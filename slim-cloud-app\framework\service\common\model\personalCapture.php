<?php

namespace common\model;

use PDO;

function getPersonakCaptureLogInfo($mac, $picname, $table)
{
    $db = \util\container\getLOGDb();
    $sth = $db->prepare("select DevType,MngType,PicUrl,SPicUrl from $table where MAC=:mac and PicName=:picname");
    $sth->bindParam(':mac', $mac, PDO::PARAM_STR);
    $sth->bindParam(':picname', $picname, PDO::PARAM_STR);
    $sth->execute();
    return $sth->fetch(PDO::FETCH_ASSOC);
}