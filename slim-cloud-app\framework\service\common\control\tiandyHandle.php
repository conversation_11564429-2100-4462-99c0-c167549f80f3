<?php
namespace common\control;
require_once __DIR__ . "/../../../util/tiandyCountryList.php";
require_once __DIR__ . "/../../common/model/tiandyAccount.php";

function encryptTiandyPassword($password)
{
    // 截取appSecret的前16个字符作为AES密钥
    $aesKey = substr(TIANDY_APP_SECRET, 0, 16);

    // 使用AES算法，ECB模式，PKCS5Padding填充
    $cipherMethod = "aes-128-ecb";

    // 对密码进行AES加密
    $encryptedBytes = openssl_encrypt($password, $cipherMethod, $aesKey, 0);
 
    return $encryptedBytes;
}

function generateTiandyAccount()
{
    $username = \util\common\randomkeys(16) . "@akuvox.com";
    if (\common\model\tiandyAccountExists($username)) {
        $username = generateTiandyAccount();
    }
    return $username;
}

function generateTiandyInterfaceHeaders($bodyStr)
{
    $nonce = \util\common\randomkeys(14);
    $bodyMd5 = md5($bodyStr);
    $timestamp = floor(microtime(true) * 1000);

    $stringToSign = "Content=$bodyMd5&AppSecret=".TIANDY_APP_SECRET."&Timestamp=$timestamp&Nonce=$nonce"; 
    $apiSign = hash('sha256', $stringToSign, false);

    $headers = [
                'API-Nonce:'.$nonce,
                'API-Sign:'.$apiSign,
                'API-AppKey:'.TIANDY_APP_KEY,
                'API-Timestamp:'.$timestamp,
                'Content-Type:application/json'
               ];
    return $headers;
}

function performTiandyHttpRequest($username, $encryptedPwd, $phoneCode, $domainAbbreviation) 
{
    $bodyStr = [
                'appKey' => TIANDY_APP_KEY,
                'userName' => $username,
                'password' => $encryptedPwd,
                'areaCode' => $phoneCode,
                'domainAbbreviation' => $domainAbbreviation,
                'phoneLocationCode' => $phoneCode
              ];

    $headers = generateTiandyInterfaceHeaders(json_encode($bodyStr));
    
    $output = \util\common\httpRequest("post", TIANDY_API_URL.'/registerUser', $headers, json_encode($bodyStr), true);
    \util\log\akcsLog::debug("output:" . $output);

    return json_decode($output, true);
}

function getTiandyCountryInfo($areaCode)
{
    $repository = new \util\countrylist\CountryList();
    $country = $repository->findByCode($areaCode);
    return $country;
}

function handleTiandyRegisterUser($request, $response)
{
    $userConf = \util\container\getUserData();
    // 只有社区和单住户的主账号请求能够注册tiandy账号
    if ($userConf['Role'] != ROLE_TYPE_PERSONNAL_MASTER && $userConf['Role'] != ROLE_TYPE_COMMUNITY_MASTER) {
        \util\log\akcsLog::debug("not master account, can not register tiandy account");
        return \util\response\setThirdLinkerResponseMessage($response, "not master account, can not register tiandy account", 500);
    }

    // 已经注册过 直接返回
    $tiandyAccount = \common\model\getTiandyAccount($userConf['NodeUUID']);
    if ($tiandyAccount) {
        \util\log\akcsLog::debug("already registered, username:" . $tiandyAccount['TiandyUserName']);
        $datas = ['first_registration' => TIANDY_APP_ALREADY_REGISTRATION];
        return \util\response\setThirdLinkerResponseMessage($response, "success", 0, $datas);
    }

    // 获取账号地区
    $phoneCode = str_replace('-', '', $userConf["PhoneCode"]);
    $countryInfo = getTiandyCountryInfo($phoneCode);
    \util\log\akcsLog::debug("phoneCode = {phoneCode}, domainAbbreviation = {domainAbbreviation}", ['phoneCode' => $phoneCode, 'domainAbbreviation' => $countryInfo->domainAbbreviation]);

    if (!$countryInfo) {
        \util\log\akcsLog::debug("get countryInfo failed, account:". $userConf['Account']);
        return \util\response\setThirdLinkerResponseMessage($response, "get countryInfo failed", STATE_PHONE_CODE_ERROR);
    }

    // 生成账号密码
    $username = generateTiandyAccount();
    $randPasswd = \util\common\randomkeys(16);
    $encryptedPwd = encryptTiandyPassword($randPasswd);

    // Perform the initial HTTP request
    $outputArr = performTiandyHttpRequest($username, $encryptedPwd, $phoneCode, $countryInfo->domainAbbreviation);

    // Retry if the initial request fails
    if ($outputArr['statusCode'] != TIANDY_API_STATUS_CODE_OK) {
        $outputArr = performTiandyHttpRequest($username, $encryptedPwd, $phoneCode, $countryInfo->domainAbbreviation);
    }

    if ($outputArr['statusCode'] == TIANDY_API_STATUS_CODE_OK) {
        \common\model\insertTiandyAccount($outputArr['content']['userId'], $username, $encryptedPwd, $countryInfo, $userConf['NodeUUID']);
        \util\log\akcsLog::debug("register tiandy account success, username:" . $username);
        $datas = ['first_registration' => TIANDY_APP_FIRST_REGISTRATION];
        return \util\response\setThirdLinkerResponseMessage($response, "success", STATE_SUCCESS, $datas);
    } else {
        \util\log\akcsLog::debug("register failed, statusMessage:" . $outputArr['statusMessage']);
        return \util\response\setThirdLinkerResponseMessage($response, $outputArr['statusMessage'], 500);
    }
}

function getTiandyUserConf($request, $response)
{
    $userConf = \util\container\getUserData();

    // apt下主从账号使用同一个tiandy账号
    $tiandyAccount = \common\model\getTiandyAccount($userConf['NodeUUID']);

    if ($tiandyAccount) {
        $datas = [
            'username' => $tiandyAccount['TiandyUserName'],
            'password' => $tiandyAccount['TiandyPasswd'],
            'code' => $tiandyAccount['CountryCode'],
            'pinyin' => $tiandyAccount['CountryPinyin'],
            'iso_code' => $tiandyAccount['CountryIsoCode'],
            'areaname' => $tiandyAccount['CountryAreaName'],
            'domain_abbreviation' => $tiandyAccount['CountryDomainAbbreviation'],
        ];
        return \util\response\setResponseMessage($response, ERR_CODE_SUCCESS, $datas);
    } else {
        \util\log\akcsLog::debug("getTiandyUserConf failed, user did not registered");
        return \util\response\setResponseMessage($response, ERR_CODE_FAILED, [] ,"user did not registered");
    }
}