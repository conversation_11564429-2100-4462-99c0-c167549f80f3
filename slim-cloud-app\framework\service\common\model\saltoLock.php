<?php

namespace common\model;

use PDO;

const DB_SALTOIQ_TABLE = 'SaltoIQ';
const DB_SALTOLOCK_TABLE = 'SaltoLock';
const DB_SALTOIQ_FIELD = [
    'Grade',
    'Active',
    'ProjectType',
    'AccountUUID',
    'CommunityUnitUUID',
    'PersonalAccountUUID'
];
const DB_SALTOLOCK_FIELD = [
    'UUID',
    'Name',
    'ThirdUUID',
    'IQUUID',
    'DeviceUUID',
    'Relay',
    'RBACDataGroupUUID'
];
const DB_SALTOLOCK_JOIN_ALTOIQ_FIELD = [
    'SaltoLock.UUID',
    'SaltoLock.Name',
    'SaltoLock.ThirdUUID',
    'SaltoLock.IQUUID',
    'SaltoLock.DeviceUUID',
    'SaltoLock.Relay',
    'SaltoLock.RBACDataGroupUUID',
    'SaltoIQ.Grade',
    'SaltoIQ.Active',
    'SaltoIQ.ProjectType',
    'SaltoIQ.AccountUUID',
    'SaltoIQ.CommunityUnitUUID',
    'SaltoIQ.PersonalAccountUUID'
];

// 获取位于项目下的所有lock
function getSaltoLockListByAccountUUID($projectUUID)
{
    return \util\container\medooDb()->select(
        DB_SALTOLOCK_TABLE,
        ['[><]SaltoIQ' => ['IQUUID' => 'UUID']],
        DB_SALTOLOCK_JOIN_ALTOIQ_FIELD,
        ['AND' => ['SaltoIQ.AccountUUID' => $projectUUID]]
    );
}

// 获取位于社区public area的所有lock
function getPublicSaltoLockList($projectUUID)
{
    return \util\container\medooDb()->select(
        DB_SALTOLOCK_TABLE,
        ['[><]SaltoIQ' => ['IQUUID' => 'UUID']],
        DB_SALTOLOCK_JOIN_ALTOIQ_FIELD,
        ['AND' => ['SaltoIQ.AccountUUID' => $projectUUID, 'SaltoIQ.Grade' => COMMUNITY_DEVICE_TYPE_PUBLIC]]
    );
}

// 获取位于社区unit area所有lock
function getUnitSaltoLockList($projectUUID)
{
    return \util\container\medooDb()->select(
        DB_SALTOLOCK_TABLE,
        ['[><]SaltoIQ' => ['IQUUID' => 'UUID']],
        DB_SALTOLOCK_JOIN_ALTOIQ_FIELD,
        ['AND' => ['SaltoIQ.AccountUUID' => $projectUUID, 'SaltoIQ.Grade' => COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT]]
    );
}

// 获取家庭独占的所有lock
function getAptSaltoLockList($nodeUUID)
{
    return \util\container\medooDb()->select(
        DB_SALTOLOCK_TABLE,
        ['[><]SaltoIQ' => ['IQUUID' => 'UUID']],
        DB_SALTOLOCK_JOIN_ALTOIQ_FIELD,
        ['AND' => ['SaltoIQ.PersonalAccountUUID' => $nodeUUID, 'SaltoIQ.Grade' => COMMUNITY_DEVICE_TYPE_PERSONAL]]
    );
}

// 获取lock信息
function getSaltoLockInfo($thirdUUID)
{
    return \util\container\medooDb()->get(
        DB_SALTOLOCK_TABLE,
        DB_SALTOLOCK_FIELD,
        ['ThirdUUID' => $thirdUUID]
    );
}

function getSaltoIQInfo($projectUUID)
{
    return \util\container\medooDb()->get(
        DB_SALTOIQ_TABLE,
        DB_SALTOIQ_FIELD,
        ['AccountUUID' => $projectUUID]
    );
}
