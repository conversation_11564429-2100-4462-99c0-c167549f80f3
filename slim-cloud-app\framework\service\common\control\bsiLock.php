<?php

namespace common\control;

require_once __DIR__ . "/../model/thirdPartyLockDevice.php";

function bindBSILock($request, $response)
{
    $userConf = \util\container\getUserData();

    //只有主账号才可以绑定
    if ($userConf['Role'] != ROLE_TYPE_COMMUNITY_MASTER && $userConf['Role'] != ROLE_TYPE_PERSONNAL_MASTER) {
        return \util\response\setResponseMessage($response, ERR_CODE_FAILED, [] ,"Accout role is not main account");
    }

    $postDatas = $request->getParsedBody();
    $bleId = $postDatas['ble_id'];
    $blePwd = $postDatas['ble_pwd'];
    $mac = $postDatas['mac'];

    if ($bleId === null || $blePwd === null || $mac === null) {
        return \util\response\setResponseMessage($response, ERR_CODE_FAILED, [] ,"body is null");
    }

    //插入绑定关系
    \common\model\addBSILock($bleId, $blePwd, $mac, $userConf);
    return \util\response\setResponseMessage($response, ERR_CODE_SUCCESS);
}