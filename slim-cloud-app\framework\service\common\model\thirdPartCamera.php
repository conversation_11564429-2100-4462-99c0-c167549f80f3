<?php

namespace common\model;

use PDO;

function getThirdPartyCameraList($resultToken, &$thirdPartyDevList, $accessDevs)
{
    $db = \util\container\getDb();
    $cameraList = array();
    //单住户第三方摄像头根据主账户查找下发
    if ($resultToken['Role'] == ROLE_TYPE_PERSONNAL_MASTER || $resultToken['Role'] == ROLE_TYPE_PERSONNAL_SLAVE) {
        $sth = $db->prepare("select UUID,Location,RtspAddress,RtspUserName,RtspPwd,MAC from PersonalThirdPartCamera 
        where PersonalAccountUUID = :uuid");
        $sth->bindParam(':uuid', $resultToken['NodeUUID'], PDO::PARAM_STR);
        $sth->execute();
        $cameras = $sth->fetchALL(PDO::FETCH_ASSOC);
        //插入列表
        \common\model\createAllCameraList($cameras, $cameraList);
    } elseif ($resultToken['Role'] == ROLE_TYPE_COMMUNITY_MASTER || $resultToken['Role'] == ROLE_TYPE_COMMUNITY_SLAVE) {
        //社区用户第三方摄像头列表下发规则：1、如果未绑定设备，根据位置下发；2、如果绑定设备，根据设备的权限下发
        //最外围第三方摄像头
        $sth = $db->prepare("select UUID,Location,RtspAddress,RtspUserName,RtspPwd,MAC,UnitID from ThirdPartCamera 
        where ProjectUUID = :project_uuid and (Grade = ".COMMUNITY_DEVICE_TYPE_PUBLIC." or Grade = ".COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT.")");
        $sth->bindParam(':project_uuid', $resultToken['MngUUID'], PDO::PARAM_STR);
        $sth->execute();
        $publicCameras = $sth->fetchALL(PDO::FETCH_ASSOC);
        //插入列表
        \common\model\createCommunityPubCameraList($publicCameras, $accessDevs, $resultToken['UnitID'], $cameraList);

        //家庭第三方摄像头
        $sth = $db->prepare("select UUID,Location,RtspAddress,RtspUserName,RtspPwd,MAC from ThirdPartCamera 
        where ProjectUUID = :project_uuid and PersonalAccountUUID = :personal_uuid and Grade = ".COMMUNITY_DEVICE_TYPE_PERSONAL);
        $sth->bindParam(':project_uuid', $resultToken['MngUUID'], PDO::PARAM_STR);
        $sth->bindParam(':personal_uuid', $resultToken['NodeUUID'], PDO::PARAM_STR);
        $sth->execute();
        $nodeCameras = $sth->fetchALL(PDO::FETCH_ASSOC);
        \common\model\createCommunityAptCameraList($nodeCameras, $accessDevs, $cameraList);

    } elseif ($resultToken['Role'] == ROLE_TYPE_COMMUNITY_PM) {
        $sth = $db->prepare("select UUID,Location,RtspAddress,RtspUserName,RtspPwd,MAC,UnitID from ThirdPartCamera 
        where ProjectUUID = :project_uuid and (Grade = ".COMMUNITY_DEVICE_TYPE_PUBLIC." or Grade = ".COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT.")");
        $sth->bindParam(':project_uuid', $resultToken['MngUUID'], PDO::PARAM_STR);
        $sth->execute();
        $publicCameras = $sth->fetchALL(PDO::FETCH_ASSOC);
        //插入列表
        \common\model\createAllCameraList($publicCameras, $cameraList);
    }

    $thirdPartyDevList['camera_dev_list'] = $cameraList;
}


function createCommunityAptCameraList($cameras, $accessDevs, &$cameraList)
{
    foreach ($cameras as $camera) {
        if ($camera['MAC'] && !array_key_exists($camera['MAC'], $accessDevs))
        {
            //绑定mac时，不在accessDevs中说明用户没有权限，跳出，否则插入摄像头列表
            continue;
        }
        else
        {
            insertCameraList($camera, $cameraList);
        }
    }
}

function createCommunityPubCameraList($cameras, $accessDevs, $unitID, &$cameraList)
{
    foreach ($cameras as $camera) {
        //绑定mac时，不在accessDevs中说明用户没有权限，跳过
        if ($camera['MAC'] && !array_key_exists($camera['MAC'], $accessDevs))
        {
            continue;
        } 
        //未绑定mac的，不允许获取其他楼栋的设备
        else if(!$camera['MAC'] && $camera['UnitID'] != 0 && $camera['UnitID'] != $unitID)
        {
            continue;
        }
        insertCameraList($camera, $cameraList);
    }
}

function createAllCameraList($cameras, &$cameraList)
{
    foreach ($cameras as $camera) {
        insertCameraList($camera, $cameraList);
    }
}

function insertCameraList($camera, &$cameraList)
{
    $cameraNode = array();
    $cameraNode['uuid'] = $camera['UUID'];
    $cameraNode['location'] = $camera['Location'];
    $cameraNode['rtsp_url'] = $camera['RtspAddress'];
    $cameraNode['username'] = $camera['RtspUserName'];
    $cameraNode['password'] = $camera['RtspPwd'];
    $cameraNode['bonded_device'] = $camera['MAC'];
    array_push($cameraList, $cameraNode);
}
