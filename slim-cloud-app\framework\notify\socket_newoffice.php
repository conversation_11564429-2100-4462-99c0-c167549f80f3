<?php

require_once(dirname(__FILE__) . '/notify_newoffice.php');
require_once(dirname(__FILE__) . '/../config/define.php');
require_once(dirname(__FILE__) . '/proto/proto_office.php');
require_once(dirname(__FILE__) . '/socket.php');

class CJsonMessageNotify extends CSocket
{
    /**
     * @param mixed $data   json格式消息体, 详见：http://192.168.10.102:8071/x/LoB4B  之 “6 web和应用后台通信协议”
     * 示例：
     * { 
     *     "msg_type":  "xxxx",           //区分大小写
     *     "trace_id":   "xxxx",          //链路请求ID
     *     "timestamp": 1726688999000000, //微秒, long long
     *     "data": { }                    //data内部的消息都是string
     * }
     */
    public function copy($data)
    {
        $this->byte->writeProtobuf($data);
        $this->setMsgHead();
        $this->sendMsg();
        return;
    }
}
