<?php

namespace util\redisManage;

use Redis;

class RedisManage
{
    public $instance = null;

    public function __construct()
    {
        if (ENABLE_REDIS_SENTINEL == 0) {
            $this->instance = $this->ConnectRedis(REDISIP, REDISSOCKET);
        } else {
            $sentinels = explode(',', REDIS_SENTINEL_HOSTS);
            //随机打乱数组
            shuffle($sentinels);
            foreach ($sentinels as $val) {
                $sentinel_redis = new Redis();
                $sentinel = explode(':', $val);
                //连接sentinel
                $ret = $sentinel_redis->connect($sentinel[0], $sentinel[1], REDIS_SENTINEL_TIMEOUT);
                if ($ret) {
                    $result = $sentinel_redis->rawCommand('SENTINEL', 'masters');
                    $datas = $this->parseArrayResult($result);
                    //不考虑多主
                    $redis = $this->ConnectRedis($datas[0]["ip"], $datas[0]["port"]);
                    if ($redis) {
                        //判断sentinel告知的是不是真正的主库
                        $result = $redis->rawCommand('INFO', 'Replication');
                        if (strstr($result, "role:master")) {
                            $this->instance = $redis;
                            break; //建立有效主节点后退出循环
                        } else {
                            //从别的sentinel那里继续查找
                        }
                    }
                }
            }
        }
    }

    public function connectRedis($ip, $port)
    {
        $redis = new Redis();
        $ret = $redis->connect($ip, $port);
        if ($ret) {
            $redis->auth(REDISPW);
            return $redis;
        } else {
        }
        return null;
    }

    public function getRedisInstance()
    {
        return $this->instance;
    }

    //这个方法可以将以上sentinel返回的信息解析为数组
    public function parseArrayResult(array $data)
    {
        $result = array();
        $count = count($data);
        for ($i = 0; $i < $count;) {
            $record = $data[$i];
            if (is_array($record)) {
                $result[] = $this->parseArrayResult($record);
                $i++;
            } else {
                $result[$record] = $data[$i + 1];
                $i += 2;
            }
        }
        return $result;
    }
}

function getAppMotionStatus($userAccount)
{
    $redisKey = "motion";
    $redis = \util\container\getRedis();
    $redis->select(1);
    $appMotionStatus = $redis->hGet($redisKey, $userAccount);
    if ($appMotionStatus == null) {
        $appMotionStatus = "1"; //默认是接收的
    }
    return strval($appMotionStatus);
}

function getNonce($mac)
{
    $redis = \util\container\getRedis();
    $redis->select(4);

    $nonce = $redis->get($mac);
    return $nonce ? $nonce : "";
}

function setDnd($userAccount, $dndData)
{
    $redis = \util\container\getRedis();
    $redis->select(3);
    $redis->hSet($userAccount, "dndinfo", $dndData);
}

function getDnd($userAccount)
{
    $redis = \util\container\getRedis();
    $redis->select(3);
    $appDndInfo = $redis->hGet($userAccount, "dndinfo");
    return $appDndInfo;
}