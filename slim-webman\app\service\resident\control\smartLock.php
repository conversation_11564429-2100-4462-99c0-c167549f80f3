<?php

namespace  resident\control;

require_once __DIR__ . "/../model/sl50Lock.php";
require_once __DIR__ . "/../model/smartLock.php";
require_once __DIR__ . "/../../common/model/devices.php";
require_once __DIR__ . "/../../common/model/personalDevices.php";

function getSmartLockList($request, $response)
{
    $userConf = \util\container\getUserData();
    $smartLockList = \resident\model\getSmartLockList($userConf["NodeUUID"]);
    \util\log\akcsLog::debug("getSmartLockList: smartLockList = " . json_encode($smartLockList));
    if (empty($smartLockList)) {
        return \util\response\setResponseMessage($response, ERR_CODE_SUCCESS);
    }

    $lockDevList = [];
    foreach ($smartLockList as $smartLockInfo) {
        $lockDevice = [
            'name' => strval($smartLockInfo['Name']),
            'uuid' => strval($smartLockInfo['UUID']),
            'keep_alive' => intval($smartLockInfo['KeepAlive']),
            'online' => strval(isSmartLockOnline($smartLockInfo)),
            'device_style' => strval(getSmartLockStyle($smartLockInfo['Model'])),
            'battery_level' => $smartLockInfo['BatteryLevel'] ? intval($smartLockInfo['BatteryLevel']): 0,
            'wifi_status' => $smartLockInfo['WifiStatus'] ? strval($smartLockInfo['WifiStatus']) : strval("0")
        ];

        $bondedDevices = getLinkDeviceInfo($smartLockInfo, $userConf['IsPer']);
        if (!empty($bondedDevices) && $bondedDevices["mac"]) {
            $lockDevice['bonded_devices'] = $bondedDevices;
        }

        if ($smartLockInfo['Model'] == SMARTLOCK_MODLE_SL50) {

            $sl50LockInfo = \resident\model\getSL50LockInfoBySmartLockUUID($smartLockInfo['UUID']);
            \util\log\akcsLog::debug("getSmartLockList: sl50LockInfo = " . json_encode($sl50LockInfo));
            $lockDevice['sip'] = strval($sl50LockInfo['SipAccount']);
            $lockDevice['rtsp_pwd'] = strval($sl50LockInfo['RtspPwd']);
        }

        $lockDevList[] = $lockDevice;
    }

    $datas = [
        "lock_dev_list" => $lockDevList,
    ];
    return \util\response\setResponseMessage($response, ERR_CODE_SUCCESS, $datas);
}

function getSmartLockStyle($model)
{
    $lock_style = "";
    if ($model == SMARTLOCK_MODLE_SL20) {
        $lock_style = SMARTLOCK_MQTT_CLIENT_FLAG_SL20;
    } else if ($model == SMARTLOCK_MODLE_SL50) {
        $lock_style = SMARTLOCK_MQTT_CLIENT_FLAG_SL50;
    } 
    return $lock_style;
}

function isSmartLockOnline($lockInfo)
{
    if (empty($lockInfo['LastConnectedTime'])) {
        return 0;
    }

    // 将日期时间字符串转换为时间戳
    $updateTime = strtotime($lockInfo['LastConnectedTime']);
    $currentTime = time();

    // 检查时间差是否小于 24 小时（86400 秒）
    return ($currentTime - $updateTime) < SECONDS_IN_A_DAY;
}

function getLinkDeviceInfo($lockInfo, $isPersonal)
{
    $lockLinkDeviceInfo = [];
    if (!$lockInfo["DeviceUUID"]) {
        return $lockLinkDeviceInfo;
    }

    if ($isPersonal) {
        $bindDeviceInfo = \common\model\getPersonalDevicesInfoByUUID($lockInfo["DeviceUUID"]);
    } else {
        $bindDeviceInfo = \common\model\getDevicesInfoByUUID($lockInfo["DeviceUUID"]);
    }
    
    $lockLinkDeviceInfo["mac"] = $bindDeviceInfo['MAC'] ? strval($bindDeviceInfo['MAC']) : "";
    $lockLinkDeviceInfo["relay_id"] = intval(\util\utility\bitValueToIndex($lockInfo["Relay"]));
   
    return $lockLinkDeviceInfo;
}

function openSL20Door($request, $response)
{
    $postDatas = $request->getParsedBody();
    $userConf = \util\container\getUserData();
    $lockUUID = $postDatas['lock_uuid'];

    //判断lock_uuid和对应的用户token之间的权限关系
    $smartLockInfo = \resident\model\getSmartLockInfo($lockUUID);
    if (!$smartLockInfo) {
        return \util\response\setResponseMessage($response, ERR_CODE_SMARTLOCK_NOT_EXIST);
    }

    if ($smartLockInfo['PersonalAccountUUID'] == $userConf['NodeUUID']) {
        $redis = \util\container\getRedis();
        $redis->select(REDIS_DB_SMART_LOCK);
        $openDoorRelat = "initiator_" . $userConf['UserAccount'];
   
        if ($redis->hset($lockUUID, $userConf['UserAccount'], $openDoorRelat)) {
            // 设置过期时间
            $redis->expire($lockUUID, SMART_LOCK_OPEN_EXPIRE_TIME);
            
            // 若开启保活，将消息推送给锁
            if ($smartLockInfo['KeepAlive']) {
                smartlockUpdateNotify($lockUUID, $smartLockInfo['Model']);
            }

            return \util\response\setResponseMessage($response, ERR_CODE_SUCCESS);
        } else {
            \util\log\akcsLog::debug("openSL20Door: Failed to set Redis key");
        }
    }
    return \util\response\setResponseMessage($response, ERR_CODE_NO_PERMISSION);
}

function exitOpenSL20Door($request, $response)
{
    $postDatas = $request->getParsedBody();
    $userConf = \util\container\getUserData();
    $lockUUID = $postDatas['lock_uuid'];

    //判断lock_uuid和对应的用户token之间的权限关系
    $smartLockInfo = \resident\model\getSmartLockInfo($lockUUID);
    if (!$smartLockInfo) {
        return \util\response\setResponseMessage($response, ERR_CODE_SMARTLOCK_NOT_EXIST);
    }

    if ($smartLockInfo['PersonalAccountUUID'] == $userConf['NodeUUID']) {
        $redis = \util\container\getRedis();
        $redis->select(REDIS_DB_SMART_LOCK);
        if ($redis->hdel($lockUUID, $userConf['UserAccount']) > 0) {
            // 若开启保活，将消息推送给锁
            if ($smartLockInfo['KeepAlive']) {
                smartlockUpdateNotify($lockUUID, $smartLockInfo['Model']);
            }
            return \util\response\setResponseMessage($response, ERR_CODE_SUCCESS);
        } else {
            \util\log\akcsLog::debug("exitOpenSL20Door: Failed to delete Redis key");
        }
    }
    return \util\response\setResponseMessage($response, ERR_CODE_NO_PERMISSION);
}

function unlockSL50SmartLock($request, $response)
{
    $postDatas = $request->getParsedBody();
    $traceID = $postDatas['trace_id'];
    $lockUUID = $postDatas['lock_uuid'];

    // 判断lock_uuid和对应的用户token之间的权限关系
    $smartLockInfo = \resident\model\getSmartLockInfo($lockUUID);
    if (!$smartLockInfo) {
        return \util\response\setResponseMessage($response, ERR_CODE_SMARTLOCK_NOT_EXIST);
    }
    
    $userConf = \util\container\getUserData();
    if ($smartLockInfo['PersonalAccountUUID'] == $userConf['NodeUUID']) {
        $redis = \util\container\getRedis();
        $redis->select(REDIS_DB_SMART_LOCK);
        $openDoorInitaitor = "initiator_" . $userConf['UserAccount'];
        if ($redis->hset($lockUUID, $userConf['UserAccount'], $openDoorInitaitor)) {
            $redis->expire($lockUUID, SMART_LOCK_OPEN_EXPIRE_TIME);
            unlockSL50SmartLockNotify($lockUUID, $traceID);
            return \util\response\setResponseMessage($response, ERR_CODE_SUCCESS);
        } else {
            \util\log\akcsLog::debug("unlockSL50SmartLock failed to set Redis key, lockuuid = {$lockUUID}, userAccount = {$userConf['UserAccount']} ");
        }
    }
    return \util\response\setResponseMessage($response, ERR_CODE_NO_PERMISSION);
}