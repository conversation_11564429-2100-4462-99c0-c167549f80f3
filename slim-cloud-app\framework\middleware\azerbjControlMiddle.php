<?php
require_once(__DIR__ . '/../service/common/model/awsRedirect.php');

class AzerbjControlMiddle
{
    private $controlUriList = ['/login_conf', '/userconf'];
    public function __invoke($request, $response, $next)
    {
        $agent = $request->getHeader<PERSON><PERSON>('User-Agent');
        $uri = $request->getUri()->getPath();

        //校验是否是阿萨拜疆用户使用小睿在登录，若是则进行强制拦截
        if(in_array($uri, $this->controlUriList) &&  $agent != 'ASBJ' && \common\model\checkAzerbjRedirect()) 
        {
            if($uri == '/login_conf') 
            {
                $datas = [
                    "intercept_mode" => 2, //使用登录拦截模式
                    "word_key" => LOGIN_INTERCEPT_WORD_KEY2 //提示词条
                ];
                //可进行日志统计，没有这类日志后就可以去掉此中间件了
                \util\log\akcsLog::debug("Azerbaijan smartplus login_conf request");

                return \util\response\setResponseMessage($response, ERR_CODE_INTERCEPT_OK, $datas);
            }
            else
            {
                //可进行日志统计，没有这类日志后就可以去掉此中间件了 
                \util\log\akcsLog::debug("Azerbaijan smartplus userconf request");
                //不能用setResponseMessage，不然app会一直refresh token 退不出去
                return \util\response\setResponseMessage($response, ERR_CODE_FAILED, [] ,"please use Splus.AZ"); 
            }
        }
        
        $response = $next($request, $response);
        return $response;
    }
}

