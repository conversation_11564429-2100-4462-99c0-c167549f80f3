<?php

namespace common\model;

use PDO;

function checkFaceFlag($node)
{
    $db =  \util\container\getDb();
    $sth = $db->prepare("select Flags from PersonalAccountCnf where Account = :account");
    $sth->bindParam(':account', $node, PDO::PARAM_STR);
    $sth->execute();
    $result = $sth->fetch(PDO::FETCH_ASSOC);
    if ($result) {
        return \util\utility\switchHandle($result['Flags'], PersonlAccountCnfFlagsFace);
    }
    return 0;
}

function getMotionStatus($account, &$enablMotion, &$motionTime)
{
    $db =  \util\container\getDb();
    $sth = $db->prepare("/*master*/ select EnableMotion, MotionTime from PersonalAccountCnf where Account = :account");
    $sth->bindParam(':account', $account, PDO::PARAM_STR);
    $sth->execute();
    $result = $sth->fetch(PDO::FETCH_ASSOC);

    $enablMotion = $result['EnableMotion'];
    $motionTime = $result['MotionTime'];
}

function updateMotionStatus($motionTime, $enableMotion, $userAccount)
{
    $db =  \util\container\getDb();
    $sth = $db->prepare("UPDATE PersonalAccountCnf SET EnableMotion = :enable_motion, MotionTime = :motion_time WHERE Account = :account");
    $sth->bindParam(':motion_time', $motionTime, PDO::PARAM_INT);
    $sth->bindParam(':enable_motion', $enableMotion, PDO::PARAM_INT);
    $sth->bindParam(':account', $userAccount, PDO::PARAM_STR);
    $sth->execute();
}

function getCalltypeByAccount($account)
{       
    $db =  \util\container\getDb();
    $sth = $db->prepare("/*master*/ SELECT CallType,EnableRobinCall FROM PersonalAccountCnf WHERE Account = :account");
    $sth->bindParam(':account', $account, PDO::PARAM_STR);
    $sth->execute();
    $result = $sth->fetch(PDO::FETCH_ASSOC);
    
    return $result;
}

function checkIDAccessFlag($node)
{
    $result =  \util\container\medooDb()->get("PersonalAccountCnf", ["Flags"], ["Account" => $node]);

    if ($result) {
        return \util\utility\switchHandle($result['Flags'], PersonlAccountCnfFlagsIDAccess);
    }
    return 0;
}

function getDetectionInfo($account)
{
    $db =  \util\container\getDb();
    // EnableSoundDetection, SoundType暂时不上
    $sth = $db->prepare("/*master*/ select EnableMotion, MotionTime, EnablePackageDetection from PersonalAccountCnf where Account = :account");
    $sth->bindParam(':account', $account, PDO::PARAM_STR);
    $sth->execute();
    $result = $sth->fetch(PDO::FETCH_ASSOC);

    return $result;
}

