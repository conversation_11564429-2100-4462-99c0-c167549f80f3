<?php

namespace common\model;
require_once(dirname(__FILE__) . '/../../newoffice/model/officeCompany.php');

use PDO;
use util\medoo\Medoo;

require_once __DIR__ . "/account.php";

function checkIdentity()
{
    $db =  \util\container\getDb();
    $token = \util\container\getToken();
    $sth = $db->prepare("/*master*/ SELECT P.Role, P.Account, P.UserInfoUUID, P.ParentUUID, T.AppTokenEt, T.AppMainUserAccount FROM Token T JOIN PersonalAccount P ON T.Account=P.Account  where T.AppToken=:token");
    $sth->bindParam(':token', $token, PDO::PARAM_STR);
    $sth->execute();
    $resultToken = $sth->fetch(PDO::FETCH_ASSOC);

    if (!$resultToken) {
        \util\log\akcsLog::debug("the token does not exist, token = " . $token);
        return ERR_TOKEN_INVALID;
    }
    $tokenEt = intval($resultToken['AppTokenEt']);
    $timeNow = time();
    if ($tokenEt < $timeNow) {
        return ERR_TOKEN_EXPIRE;
    }

    if ($resultToken['Role'] == ROLE_TYPE_OFFICE_NEW_PER || $resultToken['Role'] == ROLE_TYPE_OFFICE_NEW_ADMIN) {
        $resultToken['ProjectType'] = PROJECT_TYPE_NEW_OFFICE;
    } else if ($resultToken['Role'] == ROLE_TYPE_OFFICE_EMPLOYEE || $resultToken['Role'] == ROLE_TYPE_OFFICE_ADMIN) {
        $resultToken['ProjectType'] = PROJECT_TYPE_OFFICE;
    } else {
        $resultToken['ProjectType'] = PROJECT_TYPE_RESIDENCE;
    }


    return $resultToken;
}

function getMultiSiteRoomInfo($userinfUUID)
{
    $db =  \util\container\getDb();
    $siteInfo = array();
    $sth = $db->prepare("SELECT Account,Role,ParentUUID FROM PersonalAccount  WHERE UserInfoUUID = :userinfUUID");
    $sth->bindParam(':userinfUUID', $userinfUUID, PDO::PARAM_STR);
    $sth->execute();
    $accountInfo = $sth->fetchAll(PDO::FETCH_ASSOC);
    foreach ($accountInfo as $key => $value) {
        $siteInfo[$key]['Account'] = $value['Account'];
        $siteInfo[$key]['Role'] = $value['Role'];
        if ($value['Role'] != ROLE_TYPE_PERSONNAL_SLAVE && $value['Role'] != ROLE_TYPE_COMMUNITY_SLAVE) {
            $siteInfo[$key]['Node'] = $value['Account'];
        } else {
            $parent = $value['ParentUUID'];
            $sth = $db->prepare("SELECT Account FROM PersonalAccount  WHERE UUID = :parent");
            $sth->bindParam(':parent', $parent, PDO::PARAM_STR);
            $sth->execute();
            $info = $sth->fetch(PDO::FETCH_ASSOC);
            $siteInfo[$key]['Node'] = $info['Account'];
        }

        if (\util\common\isNewOfficeUser($value['Role'])) //要先于isOfficeUser判断 因为里面有新办公的角色
        {
            $siteInfo[$key]['ProjectType'] = PROJECT_TYPE_NEW_OFFICE;
        }
        else if (\util\common\isOfficeUser($value['Role'])) {
            $siteInfo[$key]['ProjectType'] = PROJECT_TYPE_OFFICE;
        } 
        else {
            $siteInfo[$key]['ProjectType'] = PROJECT_TYPE_RESIDENCE;
        }
    }
    return $siteInfo;
}

//返回data数组说明如下
//data['UserAccount'] 用户账号
//data['Account'] 主账号
//data['Role'] 角色
//data['ParentID'] 个人管理员id
//data['UnitID'] 单元ID
//data['UUID'] token对应账号的UUID
//data['NodeUUID'] 主账号的UUID
function getCommInfo($account)
{
    $personalAccount = \util\container\medooDb()->get("PersonalAccount", ["ID", "Name", "Role", "ParentID", "ParentUUID", "UnitID", "SipType", "Codec", "RoomID", "SipAccount", "SipPwd", "TempKeyPermission", "Initialization", "UUID", "Switch", "PhoneCode", "Phone", "Phone2", "Phone3", "RoomNumber", "UserInfoUUID", "RoomID", "Name", "Active", "EnableSmartHome", "CreateTime", "CommunityUnitUUID", "Expire" => \util\medoo\Medoo::raw("ExpireTime < now()")], ["Account" => $account]);

    $data['IsPer'] = 0;
    if ($personalAccount['Role'] == ROLE_TYPE_PERSONNAL_SLAVE || $personalAccount['Role'] == ROLE_TYPE_PERSONNAL_MASTER) {
        $data['IsPer'] = 1;
    }

    if ($personalAccount['Role'] == ROLE_TYPE_PERSONNAL_MASTER || $personalAccount['Role'] == ROLE_TYPE_COMMUNITY_MASTER || $personalAccount['Role'] == ROLE_TYPE_COMMUNITY_PM) { //个人主账号
        $data['Account'] = $account;
        $data['MngID'] = $personalAccount['ParentID'];
        $data['MngUUID'] = $personalAccount['ParentUUID'];
        $data['AccountID'] = $personalAccount['ID'];
        $data['RoomID'] = $personalAccount['RoomID'];
        $data['TempKeyPermission'] = $personalAccount['TempKeyPermission'];
        $data['Switch'] = $personalAccount['Switch'];
        $data['NodeUUID'] = $personalAccount['UUID'];
        $data['RoomNumber'] = $personalAccount['RoomNumber'];
        $data['EnableSmartHome'] = $personalAccount['EnableSmartHome'];
        $data['NodeName'] = $personalAccount['Name'];
    } else {
        //从账号 再查主账号
        $nodePersonalAccount =  \util\container\medooDb()->get("PersonalAccount", ["ID", "Account", "ParentID", "ParentUUID", "RoomID", "TempKeyPermission", "UUID", "Switch", "RoomNumber", "Name", "RoomID", "EnableSmartHome"], ["UUID" => $personalAccount['ParentUUID']]);
        $data['Account'] = $nodePersonalAccount['Account'];
        $data['MngID'] = $nodePersonalAccount['ParentID'];
        $data['MngUUID'] = $nodePersonalAccount['ParentUUID'];
        $data['AccountID'] = $nodePersonalAccount['ID'];
        $data['RoomID'] = $nodePersonalAccount['RoomID'];
        $data['TempKeyPermission'] = $nodePersonalAccount['TempKeyPermission'];
        $data['Switch'] = $nodePersonalAccount['Switch'];
        $data['RoomNumber'] = $nodePersonalAccount['RoomNumber'];
        $data['EnableSmartHome'] = $nodePersonalAccount['EnableSmartHome'];
        $data['NodeName'] = $nodePersonalAccount['Name'];
        $data['NodeUUID'] = $nodePersonalAccount['UUID'];
    }

    $data['UserAccount'] = $account;
    $data['UnitID'] = $personalAccount['UnitID'];
    $data['UserAccountID'] = $personalAccount['ID'];
    $data['Role'] = $personalAccount['Role'];
    $data['SipType'] = $personalAccount['SipType'];
    $data['Codec'] = $personalAccount['Codec'];
    $data['SipAccount'] = $personalAccount['SipAccount'];
    $data['SipPwd'] = $personalAccount['SipPwd'];
    $data['ParentID'] = $personalAccount['ParentID'];
    $data['Codec'] = $personalAccount['Codec'];
    $data['Name'] = $personalAccount['Name'];
    $data['Initialization'] = $personalAccount['Initialization'];
    $data['UserSwitch'] = $personalAccount['Switch'];
    $data['UUID'] = $personalAccount['UUID'];
    $data['Phone'] = $personalAccount['Phone'];
    $data['Phone2'] = $personalAccount['Phone2'];
    $data['Phone3'] = $personalAccount['Phone3'];
    $data['PhoneCode'] = $personalAccount['PhoneCode'];
    $data['UserInfoUUID'] = $personalAccount['UserInfoUUID'];
    $data['Active'] = $personalAccount['Active'];
    $data['Expire'] = $personalAccount['Expire'];
    $data['CreateTime'] = $personalAccount['CreateTime'];
    $data['CommunityUnitUUID'] = $personalAccount['CommunityUnitUUID'];

    return $data;
}

function getOfficeCommInfo($account)
{
    $personalAccount = \util\container\medooDb()->get("PersonalAccount", ["ID", "Name", "Role", "ParentID", "ParentUUID", "UnitID", "SipType", "Codec", "RoomID", "SipAccount", "SipPwd", "TempKeyPermission", "Initialization", "UUID", "Switch", "PhoneCode", "Phone", "Phone2", "Phone3", "RoomNumber", "UserInfoUUID", "RoomID", "Name", "Active", "EnableSmartHome", "CreateTime", "CommunityUnitUUID", "Expire" => \util\medoo\Medoo::raw("ExpireTime < now()")], ["Account" => $account]);

    $data['Account'] = $account;
    $data['UserAccount'] = $account;
    $data['MngID'] = $personalAccount['ParentID'];
    $data['MngUUID'] = $personalAccount['ParentUUID'];
    $data['AccountID'] = $personalAccount['ID'];
    $data['RoomID'] = $personalAccount['RoomID'];
    $data['TempKeyPermission'] = $personalAccount['TempKeyPermission'];
    $data['UnitID'] = $personalAccount['UnitID'];
    $data['UserAccountID'] = $personalAccount['ID'];
    $data['Role'] = $personalAccount['Role'];
    $data['SipType'] = $personalAccount['SipType'];
    $data['Codec'] = $personalAccount['Codec'];
    $data['SipAccount'] = $personalAccount['SipAccount'];
    $data['SipPwd'] = $personalAccount['SipPwd'];
    $data['ParentID'] = $personalAccount['ParentID'];
    $data['Codec'] = $personalAccount['Codec'];
    $data['Name'] = $personalAccount['Name'];
    $data['UUID'] = $personalAccount['UUID'];
    $data['Initialization'] = $personalAccount['Initialization'];
    $data['UserSwitch'] = $personalAccount['Switch'];
    $data['UserInfoUUID'] = $personalAccount['UserInfoUUID'];
    $data['Active'] = $personalAccount['Active'];
    $data['Expire'] = $personalAccount['Expire'];
    $data['CreateTime'] = $personalAccount['CreateTime'];
    $data['PhoneCode'] = $personalAccount['PhoneCode'];
    $data['Phone'] = $personalAccount['Phone'];
    $data['Phone2'] = $personalAccount['Phone2'];
    $data['Phone3'] = $personalAccount['Phone3'];
    $data['CommunityUnitUUID'] = $personalAccount['CommunityUnitUUID'];

    //新办公角色赋值对应的company信息
    if (\util\common\isNewOfficeUser($data['Role']))
    {
        $companyInfo = \newoffice\model\getCompanyInfoByRoleAndPersonalAccountUUID($data['Role'], $data['UUID']);
        $data['OfficeCompanyUUID'] = $companyInfo['UUID'];
        $data['OfficeCompanyName'] = $companyInfo['Name'];
    }

    return $data;
}

function isAppFirstLogin($uid)
{
    $personalAccount = \util\container\medooDb()->get("PersonalAccount", ["appLoginStatus"], ["Account" => $uid]);
    if ($personalAccount) {
        return $personalAccount['appLoginStatus'] == 0;
    }
    return 0;
}

function updateAppLoginStatus($uid)
{
    $db =  \util\container\getDb();
    $sth = $db->prepare("update PersonalAccount set appLoginStatus=1,Initialization=1 where Account = :uid");
    $sth->bindParam(':uid', $uid, PDO::PARAM_STR);
    $sth->execute();
    $sth = $db->prepare("insert into AppLoginLog  (Uid) values(:uid)");
    $sth->bindParam(':uid', $uid, PDO::PARAM_STR);
    $sth->execute();
}

function updateAppPinInitStatus($uid)
{
    $db =  \util\container\getDb();
    //按位开关标示符:1=用户PIN是否初始化
    $sth = $db->prepare("update PersonalAccount set Switch=Switch | 1 where Account = :uid");
    $sth->bindParam(':uid', $uid, PDO::PARAM_STR);
    $sth->execute();
}

function checkAdvanceFeaturesExpire($node, &$land_status)
{
    $db =  \util\container\getDb();
    $sth = $db->prepare("SELECT ID,PhoneStatus FROM PersonalAccount WHERE Account = :Node AND PhoneExpireTime > now()");
    $sth->bindParam(':Node', $node, PDO::PARAM_STR);
    $sth->execute();
    $result = $sth->fetch(PDO::FETCH_ASSOC);
    if ($result) {
        $land_status = $result["PhoneStatus"];
        return false;
    } else {
        return true;
    }
}

function checkFamilyMemberControl($mng_id, $slave_account, $feature_expire)
{
    $db =  \util\container\getDb();
    $check_slave = 1;
    $sth = $db->prepare("SELECT Node, ID FROM APPSpecial WHERE Account = :account");
    $sth->bindParam(':account', $slave_account, PDO::PARAM_STR);
    $sth->execute();
    $result = $sth->fetch(PDO::FETCH_ASSOC);
    if ($result) {
        $node = $result['Node'];
        $slave_id = $result['ID'];
        $sth = $db->prepare("SELECT count(1) as count FROM APPSpecial WHERE Node = :node");
        $sth->bindParam(':node', $node, PDO::PARAM_STR);
        $sth->execute();
        $result_count = $sth->fetch(PDO::FETCH_ASSOC);
        $node_count = $result_count['count'];

        $sth = $db->prepare("SELECT AllowCreateSlaveCnt FROM PersonalAccountCnf WHERE Account = :account and Flags&1 = 1");
        $sth->bindParam(':account', $node, PDO::PARAM_STR);
        $sth->execute();
        $result_cnf = $sth->fetch(PDO::FETCH_ASSOC);
        if ($result_cnf) {
            $allow_cnt = $result_cnf['AllowCreateSlaveCnt'];
            if ($allow_cnt < $node_count) {
                if (!$feature_expire && \common\model\checkCommunityFeaturePlan($mng_id, FeatureItemFamilyControl)) {
                    $sth = $db->prepare("SELECT count(1) as count FROM APPSpecial WHERE Node = :node and ID < :id");
                    $sth->bindParam(':node', $node, PDO::PARAM_STR);
                    $sth->bindParam(':id', $slave_id, PDO::PARAM_INT);
                    $sth->execute();
                    $result_less = $sth->fetch(PDO::FETCH_ASSOC);
                    $less_count = $result_less['count'];
                    if ($less_count >= $allow_cnt) {
                        $check_slave = 0;
                    }
                }
            }
        }
    }
    return $check_slave;
}

function checkAccountExpire(&$appSipPwd, &$appMainSipPwd, &$appConf, $resultToken)
{
    $db =  \util\container\getDb();

    $sthExpire = $db->prepare("select unix_timestamp(now()) as now, unix_timestamp(ExpireTime) as expire from PersonalAccount where Account = :account");
    $sthExpire->bindParam(':account', $resultToken['UserAccount'], PDO::PARAM_STR);
    $sthExpire->execute();
    $retExpire = $sthExpire->fetch(PDO::FETCH_ASSOC);
    if (($retExpire['expire'] != 0) && ($retExpire['now'] > $retExpire['expire'])) {
        //当账号过期的时候,sip passwd不下发
        $appSipPwd =  "";
        $appMainSipPwd =  "";
    }

    //超过2038年 时间戳溢出
    if ($retExpire['expire'] == 0) {
        $appConf['surplus_expire_day'] = -100000;
        $appConf['account_expire'] = APP_ACCOUNT_NOT_EXPIRED;
    } else {
        $expire_day = ceil(($retExpire['expire'] - $retExpire['now']) / (3600 * 24));
        $appConf['surplus_expire_day'] = $expire_day;
        //account_expire 1-过期 0-未过期
        $appConf['account_expire'] = ($retExpire['expire'] <= $retExpire['now']) ? APP_ACCOUNT_HAS_EXPIRED : APP_ACCOUNT_NOT_EXPIRED;
    }

    if ($appConf['account_expire'] == APP_ACCOUNT_NOT_EXPIRED && \common\model\checkExpireEmailOff($resultToken['MngID'])) {
        $appConf['account_expire'] = APP_ACCOUNT_NOT_EXPIRED;
        $appConf['surplus_expire_day'] = -100000;
    }

    if ($appConf['account_expire'] == APP_ACCOUNT_NOT_EXPIRED && checkInstallerNotTipsExpire($resultToken['UserAccount'])) {
        $appConf['account_expire'] = APP_ACCOUNT_NOT_EXPIRED;
        $appConf['surplus_expire_day'] = -100000;
    }
}

function checkAccountExpire2(&$appSipPwd, &$appMainSipPwd, &$appConf, $resultToken)
{
    $db =  \util\container\getDb();
    //账号过期以各自为准，落地过期以主账号为准
    if ($resultToken['Role'] == ROLE_TYPE_PERSONNAL_SLAVE || $resultToken['Role'] == ROLE_TYPE_COMMUNITY_SLAVE) {
        $sthExpire = $db->prepare("select unix_timestamp(now()) as now, unix_timestamp(S.ExpireTime) as expire,unix_timestamp(M.PhoneExpireTime) as land_expire, M.PhoneStatus from PersonalAccount S left join PersonalAccount M on S.ParentUUID = M.UUID where S.Account =:account");
    } else {
        $sthExpire = $db->prepare("select unix_timestamp(now()) as now, unix_timestamp(ExpireTime) as expire,unix_timestamp(PhoneExpireTime) as land_expire, PhoneStatus from PersonalAccount where Account = :account");
    }
    $sthExpire->bindParam(':account', $resultToken['UserAccount'], PDO::PARAM_STR);
    $sthExpire->execute();
    $retExpire = $sthExpire->fetch(PDO::FETCH_ASSOC);

    if (($retExpire['expire'] != 0) && ($retExpire['now'] > $retExpire['expire'])) {
        //当账号过期的时候,sip passwd不下发
        $appSipPwd = "";
        $appMainSipPwd = "";
    }

    if ($resultToken['Role'] == ROLE_TYPE_COMMUNITY_MASTER || $resultToken['Role'] == ROLE_TYPE_COMMUNITY_SLAVE || $resultToken['Role'] == ROLE_TYPE_COMMUNITY_PM) {
        //超过2038年 时间戳溢出
        if ($retExpire['expire'] == 0) {
            $appConf['surplus_expire_day'] = -100000;
            $appConf['account_expire'] = APP_ACCOUNT_NOT_EXPIRED;
        } else {
            $expire_day = ceil(($retExpire['expire'] - $retExpire['now']) / (3600 * 24));
            $appConf['surplus_expire_day'] = $expire_day;
            //account_expire 1-过期 0-未过期
            $appConf['account_expire'] = ($retExpire['expire'] <= $retExpire['now']) ? APP_ACCOUNT_HAS_EXPIRED : APP_ACCOUNT_NOT_EXPIRED;
        }
    } elseif ($resultToken['Role'] == ROLE_TYPE_PERSONNAL_MASTER || $resultToken['Role'] == ROLE_TYPE_PERSONNAL_SLAVE) {
        if ($retExpire['expire'] == 0) {
            $appConf['account_expire'] = APP_ACCOUNT_NOT_EXPIRED;
        } else {
            $appConf['account_expire'] = ($retExpire['expire'] <= $retExpire['now']) ? APP_ACCOUNT_HAS_EXPIRED : APP_ACCOUNT_NOT_EXPIRED;
        }

        if ($retExpire['PhoneStatus']) {
            //超过2038年 时间戳溢出
            if ($retExpire['land_expire'] == 0) {
                //负数则APP不显示
                $appConf['surplus_expire_day'] = -100000;
            } else {
                $expire_day = ceil(($retExpire['land_expire'] - $retExpire['now']) / (3600 * 24));
                if ($expire_day == 0) { //这时候已经过期了0.几天  直接向下处理
                    $expire_day = -1;
                }
                $appConf['surplus_expire_day'] = $expire_day;
            }
        } else {
            $appConf['surplus_expire_day'] = -100000;
        }
    } else {
        $appConf['surplus_expire_day'] = -100000;
        $appConf['account_expire'] = APP_ACCOUNT_NOT_EXPIRED;
    }

    if ($appConf['account_expire'] == APP_ACCOUNT_NOT_EXPIRED && \common\model\checkExpireEmailOff($resultToken['MngID'])) {
        $appConf['account_expire'] = APP_ACCOUNT_NOT_EXPIRED;
        $appConf['surplus_expire_day'] = -100000;
    }

    if ($appConf['account_expire'] == APP_ACCOUNT_NOT_EXPIRED && \common\model\checkInstallerNotTipsExpire($resultToken['UserAccount'])) {
        $appConf['account_expire'] = APP_ACCOUNT_NOT_EXPIRED;
        $appConf['surplus_expire_day'] = -100000;
    }
}

function checkInstallerNotTipsExpire($account)
{
    $db =  \util\container\getDb();
    $installer = "'quantuum','zebralocksmith'";
    $sth2 = $db->prepare("select AA.Account from Account A left join PersonalAccount P 
    on A.UUID=P.ParentUUID and P.Role in(" . ROLE_TYPE_PERSONNAL_MASTER . "," . ROLE_TYPE_COMMUNITY_MASTER . ") left join Account AA 
    on A.ManageGroup=AA.ID where P.Account=:UID and AA.Account in($installer) 
    UNION
    select AA.Account from PersonalAccount P left join PersonalAccount PP 
    on P.ParentUUID=PP.UUID and P.Role in(" . ROLE_TYPE_PERSONNAL_SLAVE . "," . ROLE_TYPE_COMMUNITY_SLAVE . ") left join Account A 
    on A.UUID=PP.ParentUUID left join Account AA 
    on A.ManageGroup=AA.ID where P.Account=:UID and AA.Account in($installer)");

    $sth2->bindParam(':UID', $account, PDO::PARAM_STR);
    $sth2->execute();
    $row = $sth2->fetch(PDO::FETCH_ASSOC);
    if ($row) {
        return 1;
    }
    return 0;
}

function getVideoStorageTime($resultToken)
{
    $db =  \util\container\getDb();

    if ($resultToken['Role'] === ROLE_TYPE_PERSONNAL_MASTER || $resultToken['Role'] === ROLE_TYPE_COMMUNITY_MASTER) { //个人、社区主账号
        //根据Account查找主账号下面的设备列表
        $sth = $db->prepare("select VideoStorageTime from VideoLength where Node = :account");
        $sth->bindParam(':account', $resultToken['UserAccount'], PDO::PARAM_STR);
        $sth->execute();
        $ret = $sth->fetch(PDO::FETCH_ASSOC);
        if ($ret) {
            return $ret['VideoStorageTime'];
        }
    } elseif ($resultToken['Role'] === ROLE_TYPE_PERSONNAL_SLAVE || $resultToken['Role'] === ROLE_TYPE_COMMUNITY_SLAVE) { //个人/社区从账号
        $sth = $db->prepare("select C.VideoStorageTime from PersonalAccount A left join PersonalAccount B on B.UUID = A.ParentUUID left join VideoLength C on C.Node = B.Account where A.ID = :id");
        $sth->bindParam(':id', $resultToken['ID'], PDO::PARAM_INT);
        $sth->execute();
        $ret = $sth->fetch(PDO::FETCH_ASSOC);
        if ($ret) {
            return $ret['VideoStorageTime'];
        }
    }

    return 0;
}

function updatePhoneStatusByCalltype($calltype, $account)
{
    $db =  \util\container\getDb();
    $phoneStatus = ($calltype == 1 ? 1 : 0);
    $sth = $db->prepare("update PersonalAccount set phoneStatus = :status where Account = :account");
    $sth->bindParam(':account', $account, PDO::PARAM_STR);
    $sth->bindParam(':status', $phoneStatus, PDO::PARAM_INT);
    $sth->execute();
}

function getNFCCode($account)
{
    $db =  \util\container\getDb();
    $sth = $db->prepare("/*master*/ SELECT NFCCode FROM PersonalAccount  WHERE Account = :account");
    $sth->bindParam(':account', $account, PDO::PARAM_STR);
    $sth->execute();
    $nfcCode = $sth->fetch(PDO::FETCH_COLUMN);

    return $nfcCode;
}

function getBLEConf($account)
{
    $db =  \util\container\getDb();
    $sth = $db->prepare("/*master*/ SELECT BLECode,BLEOpenDoorType FROM PersonalAccount  WHERE Account = :account");
    $sth->bindParam(':account', $account, PDO::PARAM_STR);
    $sth->execute();
    $bleCode = $sth->fetch(PDO::FETCH_ASSOC);
    return $bleCode;
}

function getCommCodeConf($account)
{
    $db =  \util\container\getDb();
    $sth = $db->prepare("/*master*/ SELECT BLECode,NFCCode,BLEOpenDoorType FROM PersonalAccount  WHERE Account = :account");
    $sth->bindParam(':account', $account, PDO::PARAM_STR);
    $sth->execute();
    $resultCode = $sth->fetch(PDO::FETCH_ASSOC);
    return $resultCode;
}

function updateConfirmSwitch($switch, $uuid)
{
    $db = \util\container\getDb();

    if ($switch) {
        $sth = $db->prepare("UPDATE PersonalAccount SET Switch = Switch | (1 << " . PersonlAccountSwitchConfirm . ")  WHERE UUID = :uuid");
    } else {
        $sth = $db->prepare("UPDATE PersonalAccount SET Switch = Switch & ~(1 << " . PersonlAccountSwitchConfirm . ")  WHERE UUID = :uuid");
    }

    $sth->bindParam(':uuid', $uuid, PDO::PARAM_STR);
    $sth->execute();
}

function getAllSiteInfoByUserInfoUUID($userInfoUUID)
{
    return \util\container\medooDb()->select("PersonalAccount", ["Account", "ParentID", "Role", "RoomNumber", "UUID", "PhoneCode", "Phone", "Phone2", "Phone3"], ["UserInfoUUID" => $userInfoUUID], ["ORDER" => "CreateTime"]);
}

function checkSiteIsExpire($resultToken)
{
    $db =  \util\container\getDb();

    if ($resultToken['Role'] == ROLE_TYPE_PERSONNAL_SLAVE || $resultToken['Role'] == ROLE_TYPE_COMMUNITY_SLAVE) {
        $sthExpire = $db->prepare("select unix_timestamp(now()) as now, unix_timestamp(S.ExpireTime) as expire from PersonalAccount S left join PersonalAccount M on S.ParentUUID = M.UUID where S.Account =:account");
    } else {
        $sthExpire = $db->prepare("select unix_timestamp(now()) as now, unix_timestamp(ExpireTime) as expire from PersonalAccount where Account = :account");
    }
    $sthExpire->bindParam(':account', $resultToken['UserAccount'], PDO::PARAM_STR);
    $sthExpire->execute();
    $retExpire = $sthExpire->fetch(PDO::FETCH_ASSOC);

    $accountExpire = APP_ACCOUNT_HAS_EXPIRED;
    if ($retExpire['expire'] == 0) {
        $accountExpire = APP_ACCOUNT_NOT_EXPIRED;
    } else {
        $accountExpire = ($retExpire['expire'] <= $retExpire['now']) ? APP_ACCOUNT_HAS_EXPIRED : APP_ACCOUNT_NOT_EXPIRED;
    }
    return $accountExpire;
}

function isMultiSiteUser($userInfoUUID)
{
    $db = \util\container\getDb();
    $sth = $db->prepare("select count(*) > 1 as isMultiSiteUser from PersonalAccount where UserInfoUUID = :uuid");
    $sth->bindParam(':uuid', $userInfoUUID, PDO::PARAM_STR);
    $sth->execute();
    $ret = $sth->fetch(PDO::FETCH_ASSOC);
    if ($ret['isMultiSiteUser'] == "1") {
        return 1;
    } else {
        return 0;
    }
}

// 获取主站点的的sip信息
function getUserSipInfo($account)
{
    $db = \util\container\getDb();
    $sth = $db->prepare("select SipPwd,UUID,TimeZone from PersonalAccount where Account = :account");
    $sth->bindParam(':account', $account, PDO::PARAM_STR);
    $sth->execute();
    $ret = $sth->fetch(PDO::FETCH_ASSOC);
    return $ret;
}
// 更新用户接收Alarm强提醒开关设置
function updateAlarmReminder($uuid, $switch)
{
    $db = \util\container\getDb();
    $sth = $db->prepare("update PersonalAccount set EnableStrongAlarm = :switch where UUID = :uuid");
    $sth->bindParam(':switch', $switch, PDO::PARAM_INT);
    $sth->bindParam(':uuid', $uuid, PDO::PARAM_STR);
    if (!$sth->execute()) {
        \util\log\akcsLog::debug("update strong alarm reminder failed");
        return false;
    }
    return true;
}
// 获取用户接收Alarm强提醒开关设置
function getAlarmReminder($uuid)
{
    $db = \util\container\getDb();
    $sth = $db->prepare("/*master*/ select EnableStrongAlarm from PersonalAccount where UUID = :uuid");
    $sth->bindParam(':uuid', $uuid, PDO::PARAM_STR);
    $sth->execute();
    $alarmReminderSwitch = $sth->fetch(PDO::FETCH_ASSOC);
    return $alarmReminderSwitch;
}

// 获取社区/单住户从账号信息
function getProjectSubUserInfo($account)
{
    $db = \util\container\getDb();
    $sth = $db->prepare("select ID,UUID,ParentUUID,ParentID,Role from PersonalAccount where Account = :account and Role IN (:role1, :role2)");
    $sth->bindParam(':account', $account, PDO::PARAM_STR);
    $role1 = intval(ROLE_TYPE_COMMUNITY_SLAVE);
    $role2 = intval(ROLE_TYPE_PERSONNAL_SLAVE);
    $sth->bindParam(':role1', $role1, PDO::PARAM_INT);
    $sth->bindParam(':role2', $role2, PDO::PARAM_INT);
    $sth->execute();
    $ret = $sth->fetch(PDO::FETCH_ASSOC);
    return $ret;
}

function updateAccountDataVersion($accounts)
{
    $db = \util\container\getDb();
    foreach ($accounts as $value) {
        $sth = $db->prepare("update PersonalAccount set Version=UNIX_TIMESTAMP() where Account=:Account;");
        $sth->bindParam(':Account', $value, PDO::PARAM_STR);
        $sth->execute();
        \util\log\akcsLog::debug("[updateAccountDataVersion] Account=[" . $value . "]");
    }
}
