<?php

namespace common\control\TTLock;

require_once __DIR__ . '/../model/devices.php';
require_once __DIR__ . '/../model/personalDevices.php';
require_once __DIR__ . '/../model/TTLock.php';

function openDoor($userConf, $uuid)
{
    $linkMAC = "";
    $isJson = true;
    $heaader = array();
    $lockInfo = \common\model\TTLock\getLockInfo($uuid);
    if (isset($lockInfo['DeviceUUID'])) {
        $deviceInfo = \common\model\getDevicesInfoByUUID($lockInfo['DeviceUUID']);
        $linkMAC = $deviceInfo['MAC'];
        if(!$linkMAC)
        {
            $deviceInfo = \common\model\getPersonalDevicesInfoByUUID($lockInfo['DeviceUUID']);
            $linkMAC = $deviceInfo['MAC'];
        }
    }

    $data = array();
    $data['link_mac'] = $linkMAC;
    $data['lock_id'] = $lockInfo['LockId'];
    $data['initiator'] = $userConf['Name'];
    $data['lock_name'] = $lockInfo["Name"];
    $data['lock_type'] = TT_LOCK_TYPE;
    $data['personal_account_uuid'] = $userConf['NodeUUID'];
    $data['message_type'] = LINKER_MSG_TYPE_TT_OPEN_DOOR;
    $data['capture_type'] = THIRD_PARTY_LOCK_CAPTURE_TYPE_REMOTE_OPEN_DOOR;

    // 请求到cslinke进行开锁
    $response = \util\common\httpRequest(
        "post",
        "http://" . CSLINKER_HTTP_SERVER . '/app_open_tt_lock',
        $heaader,
        $data,
        $isJson,
        20
    );

    $result = ERR_CODE_OPEN_DOOR_FAILED;
    $json_data = json_decode($response, true);

    if ($json_data['code'] == 0 && $json_data['data']['err_code'] == \common\model\FAILED_TYPE_SUCCESS) {
        $result = ERR_CODE_SUCCESS;
    } 
    else if($json_data['data']['err_code'] == \common\model\THIRD_LOCK_FAILED_TYPE_TTLOCK_NOT_EXIST) {
        $result = ERR_CODE_TT_LOCK_NOT_EXIST;
    }  
    else {
        $result = ERR_CODE_OPEN_DOOR_FAILED;
    }

    \util\log\akcsLog::debug('tt open door response=' . $response);
    return $result;
}

