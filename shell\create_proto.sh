#!/bin/bash
set -euo pipefail

#added by czw
#注意AdaptOffice不处理，因为新版本AdaptOffice中包含了AK.Server的内容，无法直接生成替换，因此php旧办公这块不要去更新

slim=$1
#slim-base/slim-cloud-app/framework/notify/proto

#删除旧的
rm -f $slim/proto.php
rm -rf $slim/AK/Adapt
rm -f $slim/GPBMetadata/AKAdapt.php

#生成新的
cd $slim
protoc --php_out=$slim AK.Adapt.proto

echo "<?php" > $slim/proto.php
echo "require_once (dirname(__FILE__).'/GPBMetadata/AKAdapt.php');" >> proto.php
for i in `ls $slim/AK/Adapt`
do
   txt="require_once (dirname(__FILE__) . '/AK/Adapt/$i');"
   echo "$txt" >> $slim/proto.php
done
echo "?>" >> $slim/proto.php
