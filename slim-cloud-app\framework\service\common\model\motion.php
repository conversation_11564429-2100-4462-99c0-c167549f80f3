<?php

namespace common\model;

use PDO;

function getMotionTableName($motionID)
{
    $db = \util\container\getDb();
    $sth = $db->prepare("select Name from MonthSliceTableName where StartID <= :ID and EndID >= :ID and Name like 'PersonalMotion%'");
    $sth->bindParam(':ID', $motionID, PDO::PARAM_INT);
    $sth->execute();
    $result = $sth->fetch(PDO::FETCH_ASSOC);
    $table = "PersonalMotion";
    if ($result) {
        $table = $result["Name"];
    }
    return $table;
}

function setMotionRead($table, $motionID)
{
    //判断表名有几个下划线，确定要去AKCS/LOG库更新
    $underscoreCount = substr_count($table, '_');
    if ($underscoreCount == 1) {
        $db = \util\container\getDb();
    } else {
        $db = \util\container\getLogDb();
    }

    $sth = $db->prepare("update $table set Status = 1 where ID = :id");
    $sth->bindParam(':id', $motionID, PDO::PARAM_INT);
    $sth->execute();
}


