<?php
require_once __DIR__ . "/../resident/control/userconf.php";
require_once __DIR__ . "/../resident/control/dealAlarm.php";
require_once __DIR__ . "/../resident/control/showHoldDoor.php";
require_once __DIR__ . "/../resident/control/bleCode.php";
require_once __DIR__ . "/../resident/control/callType.php";
require_once __DIR__ . "/../resident/control/faceHandle.php";
require_once __DIR__ . "/../resident/control/nfcCode.php";
require_once __DIR__ . "/../resident/control/motion.php";
require_once __DIR__ . "/../resident/control/indoorRelayAutoCloseTime.php";
require_once __DIR__ . "/../common/control/dndHandle.php";
require_once __DIR__ . "/../common/control/thirdLockHandle.php";
require_once __DIR__ . "/../common/control/userFeedback.php";
require_once __DIR__ . "/../common/control/msgHandle.php";
require_once __DIR__ . "/../resident/control/smartControlHandle.php";
require_once __DIR__ . "/../resident/control/smartLock.php";

$gApp->setProjectType(PROJECT_TYPE_RESIDENCE);

$gApp->get('/commconf', function ($request, $response) {
    $response = \resident\control\getCommConf($request, $response);
    return $response;
});

$gApp->get('/userconf', function ($request, $response) {
    $response = \resident\control\getUserConfV30($request, $response);
    return $response;
});

// smartlock
$gApp->post('/apprest/v2/smarthome_lock/enter_open_door_mode', function ($request, $response) {
    $response = \resident\control\openSL20Door($request, $response);
    return $response;
});

$gApp->post('/apprest/v2/smarthome_lock/exit_open_door_status', function ($request, $response) {
    $response = \resident\control\exitOpenSL20Door($request, $response);
    return $response;
});

$gApp->get('/apprest/v2/smarthome_lock/lock_list', function ($request, $response) {
    $response = \resident\control\getSmartLockList($request, $response);
    return $response;
});

// sl50开锁
$gApp->get('/apprest/v2/smarthome_lock/unlock', function ($request, $response) {
    $response = \resident\control\unlockSL50SmartLock($request, $response);
    return $response;
});

$gApp->post('/apprest/v2/set_show_hold_door', function ($request, $response) {
    $response = \resident\control\setShowHoldDoor($request, $response);
    return $response;
});

$gApp->post('/setbleconf', function ($request, $response) {
    $response = \resident\control\setBleConf($request, $response);
    return $response;
});

$gApp->post('/setnfcconf', function ($request, $response) {
    $response = \resident\control\setNfcCode($request, $response);
    return $response;
});

$gApp->post('/setcalltype', function ($request, $response) {
    $response = \resident\control\setCallType($request, $response);
    return $response;
});

$gApp->post('/upload_face', function ($request, $response) {
    //获取api-version
    $apiVersion = \util\container\getApiVersion();
    
    if (floatval($apiVersion) < 6.5) {
        // V6.5之前的版本人脸裁剪通过后台csFaceCut服务实现
        $response = \resident\control\uploadFace($request, $response);
    }
    else {
        // V6.5后的人脸裁剪在APP实现，后台不需要做人脸裁剪
        $response = \resident\control\uploadFaceV65($request, $response);
    }
    
    return $response;
});

$gApp->get('/delete_face', function ($request, $response) {
    $response = \resident\control\deleteFace($request, $response);
    return $response;
});

$gApp->post('/dealpersonalalarm', function ($request, $response) {
    $response = \resident\control\dealResidentAlarm($request, $response);
    return $response;
});

// 设置免打扰配置
$gApp->post('/set_dnd', function ($request, $response) {
    $response = \common\control\setDnd($request, $response);
    return $response;
});

// 三方锁开门
$gApp->post('/third_party_door_control', function ($request, $response) {
    $response = \common\control\thirdPartyDoorControl($request, $response);
    return $response;
});
    
$gApp->post('/upload_face_dynamic', function ($request, $response) {
    $response = \resident\control\uploadFaceDynamic($request, $response);
    return $response;
});

$gApp->post('/set_motion_conf', function ($request, $response) {
    $response = \resident\control\setMotionCnf($request, $response);
    return $response;
});

$gApp->get('/get_motion_conf', function ($request, $response) {
    $response = \resident\control\getMotionCnf($request, $response);
    return $response;
});

$gApp->get('/get_face_status', function ($request, $response) {
    $response = \common\control\getFaceStatus($request, $response);
    return $response;
});

//6.7支持customer service相关feedback
$gApp->post('/feedback', function ($request, $response) {
    $response = \common\control\AppUserFeedback($request, $response);
    return $response;
});

$gApp->post('/set_msg_read', function ($request, $response) {
    $response = \common\control\setMsgRead($request, $response);
    return $response;
});

$gApp->get('/get_thirdparty_lock', function ($request, $response) {
    $response = \common\control\getThirdpartyLock($request, $response);
    return $response;
});


$gApp->get('/apprest/v2/get_all_smart_control_devices_info', function ($request, $response) {
    $response = \resident\control\getAllSmartControlInfo($request, $response);
    return $response;
});

$gApp->post('/apprest/v2/set_auto_close_time', function ($request, $response) {
    $response = \resident\control\setAutoCloseTime($request, $response);
    return $response;
});

$gApp->get('/apprest/v2/get_smart_control_device_info', function ($request, $response) {
    $response = \resident\control\getSmartControlDeviceInfo($request, $response);
    return $response;
});

