<?php

class TryCatchMiddle
{
    public function __invoke($request, $response, $next)
    {   
        try {
            $response = $next($request, $response);
            return $response;
        } catch (\PDOException $e) {
            \util\log\akcsLog::debug("Exception:".hideExceptionMsg($e->getMessage()));
            return \util\response\setResponseMessage($response, ERR_CODE_FAILED, [], hideExceptionMsg($e->getMessage()));
        }
    }
}
//隐藏敏感信息
function hideExceptionMsg($exception)
{
    $exceptionStr = (string) $exception;
    $hideInfoArr = [
        'DB_PWD' => DATABASEPWD,
        'REDIS_PW' => REDISPW,
    ];
    foreach ($hideInfoArr as $key => $val) {
        $exceptionStr = str_replace($val, "**{$key}**", $exceptionStr);
    }
    return $exceptionStr;
}