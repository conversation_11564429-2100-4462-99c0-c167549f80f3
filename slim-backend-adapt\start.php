#!/usr/bin/env php
<?php
require_once __DIR__ . '/vendor/autoload.php';
require_once __DIR__ .'/define.php';
use Workerman\Worker;

$httpWorker = new Worker("http://0.0.0.0:9405");
$httpWorker->count = 1; 

$httpWorker->onMessage = function($connection, $request) {
    $responseData = "";
    $fullUri = $request->uri(); // 获取完整的 URI
    $parsedUrl = parse_url($fullUri); 
    $uri = $parsedUrl['path']; 

    if ($uri === '/metrics') {
        // 返回版本信息
        $responseData = "version_metric{team=\"app_backend\"} " . BRANCH_OR_TAG;
    } 
    else {
        // 未匹配的 URI 返回 404
        $responseData = "404 Not Found: " . $uri;
    }

    // 设置 HTTP 响应头并返回响应数据
    $connection->send($responseData);
};




support\App::run();