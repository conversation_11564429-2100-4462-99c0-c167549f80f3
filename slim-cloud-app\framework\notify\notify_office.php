<?php

require_once(dirname(__FILE__).'/socket_office.php');

/*******************************办公消息枚举*******************************/

//office更新配置
const MSG_P2A_NOTIFY_OFFICE_INFO_UPDATE = MSG_P2A + 2000;

//权限组相关：用户修改
const MSG_P2A_NOTIFY_OFFICE_COMMUNITY_ACCOUNT_MODIFY = MSG_P2A + 2007;


function officeWebCommunityModifyNotify($changeType, $node = "", $mac = "", $officeid, $department = 0)
{
    LOG_TRACE("[OfficeWebCommunityModifyNotify]changeType=[" . $changeType . "] node=[" . $node . "] mac=[" . $mac . "] officeid=[" . $officeid . "] department=" . $department);

    $data[0] = $changeType;
    $data[1] = $node;
    $data[2] = $mac;
    $data[3] = $officeid;
    $data[4] = $department;

    $Socket = new CWebOfficeModifyNotify();
    $Socket->setMsgID(MSG_P2A_NOTIFY_OFFICE_INFO_UPDATE);
    $Socket->setMsgProjectType(PROJECT_TYPE_OFFICE);
    $Socket->copy($data);
}
