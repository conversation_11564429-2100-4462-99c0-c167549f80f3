<?php

namespace common\model;

const DB_TABLE_PERSONALDEVICES = "PersonalDevices";
const DB_FIELD_PERSONALDEVICES = ["Type", "Community", "Node", "MAC", "UUID"];

function getPersonalDevicesInfoByUUID($uuid)
{
    return \util\container\medooDb()->get(DB_TABLE_PERSONALDEVICES, DB_FIELD_PERSONALDEVICES, ["UUID" => $uuid]);
}

function getPersonalDevicesInfoByMac($mac)
{
    return \util\container\medooDb()->get(DB_TABLE_PERSONALDEVICES, DB_FIELD_PERSONALDEVICES, ["Mac" => $mac]);
}
