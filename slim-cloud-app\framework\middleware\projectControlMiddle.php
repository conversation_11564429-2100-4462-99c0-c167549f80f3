<?php
require_once(__DIR__ . '/../service/common/model/awsRedirect.php');

class ProjectControlMiddle
{
    public function __invoke($request, $response, $next)
    {
        $projectType = \util\container\getProjectType();

        if($projectType == PROJECT_TYPE_OFFICE) {
            require_once __DIR__ . "/../service/office/route.php";
        }
        else if($projectType == PROJECT_TYPE_NEW_OFFICE){
            require_once __DIR__ . "/../service/newoffice/route.php";
        }
        else {
            require_once __DIR__ . "/../service/resident/route.php";
        }

        $response = $next($request, $response);
        return $response;
    }
}

