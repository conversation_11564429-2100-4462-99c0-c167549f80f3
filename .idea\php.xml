<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="MessDetectorOptionsConfiguration">
    <option name="transferred" value="true" />
  </component>
  <component name="PHPCSFixerOptionsConfiguration">
    <option name="transferred" value="true" />
  </component>
  <component name="PHPCodeSnifferOptionsConfiguration">
    <option name="highlightLevel" value="WARNING" />
    <option name="transferred" value="true" />
  </component>
  <component name="PhpIncludePathManager">
    <include_path>
      <path value="$PROJECT_DIR$/slim-cloud-app/vendor/container-interop/container-interop" />
      <path value="$PROJECT_DIR$/slim-cloud-app/vendor/nikic/fast-route" />
      <path value="$PROJECT_DIR$/slim-cloud-app/vendor/pimple/pimple" />
      <path value="$PROJECT_DIR$/slim-cloud-app/vendor/psr/http-message" />
      <path value="$PROJECT_DIR$/slim-cloud-app/vendor/psr/container" />
      <path value="$PROJECT_DIR$/slim-cloud-app/vendor/slim/slim" />
      <path value="$PROJECT_DIR$/slim-cloud-app/vendor/composer" />
    </include_path>
  </component>
  <component name="PhpProjectSharedConfiguration" php_language_level="7.0">
    <option name="suggestChangeDefaultLanguageLevel" value="false" />
  </component>
  <component name="PhpStanOptionsConfiguration">
    <option name="transferred" value="true" />
  </component>
  <component name="PsalmOptionsConfiguration">
    <option name="transferred" value="true" />
  </component>
</project>