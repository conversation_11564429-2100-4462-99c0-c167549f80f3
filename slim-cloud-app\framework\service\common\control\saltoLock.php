<?php

namespace common\control\salto;

require_once __DIR__ . "/../../../config/define.php";
require_once __DIR__ . '/../model/devices.php';
require_once __DIR__ . '/../model/personalDevices.php';
require_once __DIR__ . '/../model/saltoLock.php';
require_once __DIR__ . '/../model/accessGroupDevices.php';
require_once __DIR__ . '/../model/thirdPartyLockCapture.php';
require_once __DIR__ . '/../../resident/model/communityInfo.php';

function transferSaltoLinkLockStatus(&$lockList)
{
    foreach ($lockList as &$lock) {
        if (strlen($lock['bonded_devices']['mac']) > 0) {
            $lock['connected'] = strval(THIRD_LOCK_CONNECTED_ONLINE);
        }
    }
}

// pm下发公共和楼栋的所有lock,无需判断权限组
function getPmSaltoLockList()
{
    $allLockList = array();
    $userConf = \util\container\getUserData();
    $projectLockList = \common\model\getSaltoLockListByAccountUUID($userConf['MngUUID']);
    foreach ($projectLockList as $row => $device) {
        if ($device['Grade'] == COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT || $device['Grade'] == COMMUNITY_DEVICE_TYPE_PUBLIC) {
            $allLockList[] = $device;
        }
    }

    if (empty($allLockList)) {
        return [];
    }

    getLinkDeviceInfo($allLockList);
    $saltoLockList = adaptSaltoApiLockList($allLockList);

    // link的设备默认在线,兼容ios处理
    transferSaltoLinkLockStatus($saltoLockList);
    return $saltoLockList;
}

// 社区账号下发pub/unit/apt所有有权限的lock
function getCommunityEnduserSaltoLockList($accessDevs)
{
    $userConf = \util\container\getUserData();
    $allLockList = \common\model\getAptSaltoLockList($userConf['NodeUUID']);
    $projectLockList = \common\model\getSaltoLockListByAccountUUID($userConf['MngUUID']);
    foreach ($projectLockList as $row => $device) {
        if ($device['Grade'] == COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT || $device['Grade'] == COMMUNITY_DEVICE_TYPE_PUBLIC) {
            $allLockList[] = $device;
        }
    }

    if (empty($allLockList)) {
        return [];
    }

    // 获取有权限的lock列表
    $accessLockList = getCommunityAccessLockList($allLockList, $accessDevs);
    // 适配app api数据格式
    $adaptApiLockList = adaptSaltoApiLockList($accessLockList);

    return $adaptApiLockList;
}

// 单住户账号下发apt内所有lock
function getPersonnalEnduserSaltoLockList()
{
    $userConf = \util\container\getUserData();
    $aptLockList = \common\model\getAptSaltoLockList($userConf['NodeUUID']);
    if (empty($aptLockList)) {
        return [];
    }

    getLinkDeviceInfo($aptLockList);
    $saltoLockList = adaptSaltoApiLockList($aptLockList);

    // link的设备默认在线,兼容ios处理
    transferSaltoLinkLockStatus($saltoLockList);
    return $saltoLockList;
}

// 转换为下发给app的数据格式
function adaptSaltoApiLockList($lockList)
{
    $adaptApiLockList = array();

    foreach ($lockList as $eachLock) {
        $adaptedLock = array(
            'brand' => 'Salto',
            'type' => SALTO_LOCK_TYPE,
            'grade' => intval($eachLock['Grade']),
            'location' => strval($eachLock['Name']),
            'uuid' => strval($eachLock['ThirdUUID']),
            'iq_uuid' => strval($eachLock['IQUUID']),
            'active_status' => intval($eachLock['Active']),
        );


        // 构建绑定设备信息
        $bondedDevices = array();
        $bondedDevices['mac'] = $eachLock['mac'] ? $eachLock['mac'] : '';
        // log运算将1248转换为0123,接口请求时开0123relay,每把锁只能绑定一个relay
        $bondedDevices['relay_id'] = $eachLock['Relay'] ? log($eachLock['Relay'], 2) : -1;

        // 将绑定设备信息添加到适配后的锁数据中
        $adaptedLock['bonded_devices'] = $bondedDevices;

        // 将适配后的锁数据添加到结果数组的末尾
        $adaptApiLockList[] = $adaptedLock;
    }

    return $adaptApiLockList;
}

function getLinkDeviceInfo(&$allLockList)
{
    foreach ($allLockList as &$lock) {
        if (!$lock['DeviceUUID']) {
            continue;
        }

        $lockType = $lock['ProjectType'];
        if ($lockType == SALTO_LOCK_PROJECT_TYPE_COMMUNITY || $lockType == SALTO_LOCK_PROJECT_TYPE_OFFICE) {
            $bindDeviceInfo = \common\model\getDevicesInfoByUUID($lock['DeviceUUID']);
        } elseif ($lockType ==  SALTO_LOCK_PROJECT_TYPE_PERSONNAL) {
            $bindDeviceInfo = \common\model\getPersonalDevicesInfoByUUID($lock['DeviceUUID']);
        }

        $lock['mac'] = $bindDeviceInfo['MAC'];
    }
}

function getCommunityAccessLockList($allLockList, $accessDevs)
{
    // 获取有权限的设备
    $accessDeviceInfo = array();
    $userConf = \util\container\getUserData();

    // 填充mac和relay到accessDeviceInfo
    foreach ($accessDevs as $MAC => $device) {
        $device_uuid = $device['UUID'];
        $accessDeviceInfo[$device_uuid]['MAC'] = $MAC;
        $accessDeviceInfo[$device_uuid]['Relay'] = $device['Relay'];
    }

    // 获取有权限的设备关联的lock列表
    $accessLockList = array();
    $community_unit_uuid = $userConf['CommunityUnitUUID'];
    foreach ($allLockList as $lock) {
        $ak_device_uuid = $lock['DeviceUUID'];

        // (同楼栋下 || 最外层设备)，没link设备的lock都有权限
        if (
            empty($ak_device_uuid) &&
            ($community_unit_uuid == $lock['CommunityUnitUUID'] || $lock['Grade'] == COMMUNITY_DEVICE_TYPE_PUBLIC)
        ) {
            array_push($accessLockList, $lock);
            continue;
        }

        // mac没有权限
        if (!array_key_exists($ak_device_uuid, $accessDeviceInfo)) {
            continue;
        }

        // 判断relay是否有权限
        $bindRelay = intval($lock['Relay']);
        $accessRelay = intval($accessDeviceInfo[$ak_device_uuid]['Relay']);
        if (\util\common\checkRelay($accessRelay, $bindRelay)) {
            $lock['mac'] = $accessDeviceInfo[$ak_device_uuid]['MAC'];
            array_push($accessLockList, $lock);
        } else {
            \util\log\akcsLog::debug("salto bind relay has no access, bindRelay = $bindRelay, accessRelay = $accessRelay");
        }
    }

    return $accessLockList;
}

function openSaltoDoor($userConf, $thirdLockUUID)
{
    $linkMAC = "";
    $isJson = true;
    $heaader = array();
    $lockInfo = \common\model\getSaltoLockInfo($thirdLockUUID);
    if (isset($lockInfo['DeviceUUID'])) {
        $deviceInfo = \common\model\getDevicesInfoByUUID($lockInfo['DeviceUUID']);
        $linkMAC = $deviceInfo['MAC'];
    }

    $data = array();
    $data['link_mac'] = $linkMAC;
    $data['uuid'] = $thirdLockUUID;
    $data['initiator'] = $userConf['Name'];
    $data['lock_name'] = $lockInfo["Name"];
    $data['lock_type'] = SALTO_LOCK_TYPE;
    $data['personal_account_uuid'] = $userConf['NodeUUID'];
    $data['message_type'] = LINKER_MSG_TYPE_SALTO_OPEN_DOOR;
    $data['capture_type'] = THIRD_PARTY_LOCK_CAPTURE_TYPE_REMOTE_OPEN_DOOR;

    // 请求到cslinke进行开锁
    $response = \util\common\httpRequest(
        "post",
        "http://" . CSLINKER_HTTP_SERVER . '/open_salto_lock',
        $heaader,
        $data,
        $isJson,
        20
    );

    // 处理返回结果
    $result = ERR_CODE_OPEN_DOOR_FAILED;
    $json_data = json_decode($response, true);
    if (empty($json_data) || !isset($json_data['code']) || !isset($json_data['data']) || !isset($json_data['data']['err_code'])) {
        $result = ERR_CODE_OPEN_DOOR_FAILED;
    } elseif ($json_data['code'] == 0 && $json_data['data']['err_code'] == \common\model\FAILED_TYPE_SUCCESS) {
        $result = ERR_CODE_SUCCESS;
    } elseif ($json_data['data']['err_code'] == \common\model\FAILED_TYPE_SALTO_TOKEN_EXPIRED) {
        // ins账号上的salto IQ token过期，返回指定错误码给APP提示错误信息
        $result = ERR_CODE_SALTO_CODE_EXPIRED;
    }

    \util\log\akcsLog::debug('salto open door response=' . $response);
    return $result;
}

// 获取锁的实时状态
function getSaltoLockRealTimeStatus($userConf, &$saltoLockList)
{
    $saltoLockList = array();

    /*
    if (!\common\model\getSaltoIQInfo($userConf['MngUUID'])) {
        return;
    }

    // 获取锁列表
    $saltoLockList = getSaltoLockList();

    // 提取锁的uuid, 拼接成逗号分隔的字符串
    $thirdUUIDString = "";
    foreach ($saltoLockList as $lock) {
        if (isset($lock['uuid'])) {
            $thirdUUIDString .= $lock['uuid'] . ',';
        }
    }

    $thirdUUIDString = rtrim($thirdUUIDString, ',');
    if (empty($thirdUUIDString)) {
        return;
    }

    // 请求锁实时状态
    $output = \util\common\httpRequest(
        'get',
        'http://' . CSLINKER_HTTP_SERVER . '/salto_lock_status?thirdUUID=' . $thirdUUIDString,
        $heaader = []
    );

    // 更新锁的状态
    $realTimeLockStatus = json_decode($output, true);
    if (isset($realTimeLockStatus['data']['list'])) {
        $realTimeLockStatusList = $realTimeLockStatus['data']['list'];

        // 创建一个映射以便于快速查找
        $uuidToConnectedMap = [];
        foreach ($realTimeLockStatusList as $lockInfo) {
            if (isset($lockInfo['thirdUUID']) && isset($lockInfo['connected'])) {
                $uuidToConnectedMap[$lockInfo['thirdUUID']] = $lockInfo['connected'] ? '1' : '0';
            }
        }

        // 更新 saltoLockList 中的 connected 值
        foreach ($saltoLockList as &$lock) {
            if (isset($lock['uuid']) && isset($uuidToConnectedMap[$lock['uuid']])) {
                $lock['connected'] = $uuidToConnectedMap[$lock['uuid']];
            }
        }
        unset($lock); // 释放引用
    }
    */
}
