<?php
namespace app\middleware;

use Webman\MiddlewareInterface;
use Webman\Http\Response;
use Webman\Http\Request;
use PDO;

require_once __DIR__ . "/../util/redisManage.php";
require_once __DIR__ . "/../util/utility.php";
require_once __DIR__ . "/../util/medoo.php";

/**
 * Class GlobalResourceMiddle
 * @package app\middleware
 */
class GlobalResourceMiddle implements MiddlewareInterface
{
    public function process(Request $request, callable $next): Response
    {
        \util\container\GlobalApp::getInstance()->set('akcs_trace_id', \util\utility\createTraceID(16));
        \util\container\GlobalApp::getInstance()->set('akcs_medoo_db', $this->initMedooDb());        
        \util\container\GlobalApp::getInstance()->set('akcs_redis', $this->initRedis());

        $response = $next($request);
        \util\container\GlobalApp::$instance = null;
        return $response;
    }

    private function initMedooDb() {
        $database = new \util\medoo\Medoo([
            'database_name' => 'AKCS',
            'server' => AKCS_DATABASEIP,
            'port' => AKCS_DATABASEPORT,
            'username' => 'dbuser01',
            'password' => DATABASEPWD
        ]);
        return $database;
    }

    private function initRedis() {
        $RedisManage = new \util\redisManage\RedisManage();
        return $RedisManage->getRedisInstance();
    }
}
