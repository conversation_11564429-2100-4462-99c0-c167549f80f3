<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: AK.AdaptOffice.proto

namespace GPBMetadata;

class AKAdaptOffice
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();

        if (static::$is_initialized == true) {
          return;
        }
        $pool->internalAddGeneratedFile(hex2bin(
            "0a94020a14414b2e41646170744f66666963652e70726f746f120e414b2e" .
            "41646170744f666669636522760a155765624f66666963654d6f64696679" .
            "4e6f7469667912100a086d61635f6c69737418012003280912130a0b6368" .
            "616e67655f7479706518022001280d120c0a046e6f646518032001280912" .
            "110a096f66666963655f696418042001280d12150a0d6465706172746d65" .
            "6e745f696418052001280d226c0a14504d4f66666963654163636f756e74" .
            "52656e657712110a09636f6d6d756e697479180120012809120d0a05656d" .
            "61696c180220012809120f0a07706d5f6e616d6518032001280912130a0b" .
            "6163636f756e745f6e756d180420012805120c0a046c6973741805200128" .
            "09620670726f746f33"
        ));

        static::$is_initialized = true;
    }
}
