<?php

namespace common\model;

use PDO;

function getNoMonitorList()
{
    $db =  \util\container\getDb();
    $sth = $db->prepare("select VersionNumber from VersionModel where IsNoMonitor = 1");
    $sth->execute();
    $result = $sth->fetchAll(PDO::FETCH_COLUMN);
    return $result;
}

function getSupportPackageDetectionList()
{
    $db =  \util\container\getDb();
    $sth = $db->prepare("select VersionNumber,VersionName from VersionModel where IsPackageDetection = 1");
    $sth->execute();
    $result = $sth->fetchAll(PDO::FETCH_ASSOC);
    return $result;
}

function getSupportSoundDetectionList()
{
    $db =  \util\container\getDb();
    $sth = $db->prepare("select VersionNumber,VersionName from VersionModel where IsSoundDetection = 1");
    $sth->execute();
    $result = $sth->fetchAll(PDO::FETCH_ASSOC);
    return $result;
}