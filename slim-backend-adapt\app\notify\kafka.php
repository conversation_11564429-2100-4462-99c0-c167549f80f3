<?php
require_once(dirname(__FILE__) . '/../config/define.php');

/**
 * @description: notifyKafka
 */
class notifyKafka
{
    //topic
    public static $topicName = 'notify_app_backend';

    /**
     * @description: 发送消息
     * @param $data
     * @param $topic
     * @return void
     */
    public static function sendMsg($data, $topicName = '', $retry = true) {
        try {
            $producer = self::createProducer();
            $topicName = empty($topicName) ? self::$topicName : $topicName;
            $topic = $producer->newTopic($topicName);
            if (strlen($data) >= 1024000) 
            {
                \util\log\akcsLog::debug('cancel send to kafka, data size more than 1024000');
                return false;
            }
            \util\log\akcsLog::debug('ready to send to kafka, topic:' . $topicName . ',data:' . print_r($data, true));
            //RD_KAFKA_PARTITION_UA 不指定分区 自动分配
            $topic->produce(RD_KAFKA_PARTITION_UA, 0, $data);
            //非阻塞调用
            $producer->poll(0);
            //设置超时10s，如果未成功，消息可能投递失败，也可以设置为-1，无限等待。
            $result = $producer->flush(10000);
            if (RD_KAFKA_RESP_ERR_NO_ERROR !== $result) 
            {
                \util\log\akcsLog::debug("notifyKafka Was unable to flush, messages might be lost! data:". print_r($data, true) . ', topic:' . $topicName);
                return false;
            }
            \util\log\akcsLog::debug('send to kafka success, topic:' . $topicName . ',data:' . print_r($data, true));
            return true;
        } 
        catch (\Exception $e) 
        {
            if ($retry) 
            {
                \util\log\akcsLog::debug("notifyKafka sendMsg error, ready to retry, data:".print_r($data, true) . ', topic:' . $topicName . ' error:' . $e->getMessage());
                return self::sendMsg($data, $topicName, false);
            } 
            else 
            {
                \util\log\akcsLog::debug("notifyKafka sendMsg error, data:".print_r($data, true) . ', topic:' . $topicName . ' error:' . $e->getMessage());
                return false;
            }
        }
    }

    /**+
     * @description: 创建producer
     * @return \RdKafka\Producer
     */
    private static function createProducer() {
        $conf = new \RdKafka\Conf();
        $conf->set('metadata.broker.list', KAFKA_BROKER_LIST);
        $producer = new RdKafka\Producer($conf);
        return $producer;
    }
}

//基类
class CKafka
{
    public $byte;
    //以下为消息头字段定义
    private $id;
    private $from=0101;
    private $param1=0101;
    private $param2=0101;
    //const ID_LENGTH=4;
    const ID_LENGTH=4;
    const FROM_LENGTH=4;
    const PARAM1_LENGTH=4;
    const PARAM2_LENGTH=4;
    public function __set($name, $value)
    {
        $this->$name=$value;
    }

    public function __construct()
    {
        $this->byte=new Byte();
    }

    public function setMsgID($id)
    {
        $this->id = $id;
        return ;
    }
    public function setMsgFrom($from)
    {
        $this->from = $from;
        return ;
    }
    public function copy($data)
    { //须由子类重载
    }
    /*
     *构造消息头
     *消息头=length+id+from+param1+param2
     *length是总长度(4字节),id消息类型(4字节),from暂时无用(4字节),param1/param2(预留字段,均为4字节)
     */
    private function getHeader()
    {
        $length=$this->byte->getLength();
        $length=intval($length)+self::ID_LENGTH+self::FROM_LENGTH+self::PARAM1_LENGTH+self::PARAM2_LENGTH;
        return pack('N', $length);
    }
    private function getMsgId()
    {
        return pack("N", $this->id);
    }
    private function getMsgFrom()
    {
        return pack('N', $this->from);
    }
    private function getMsgParam1()
    {
        return pack('N', $this->param1);
    }
    private function getMsgParam2()
    {
        return pack('N', $this->param2);
    }
    //构造消息头
    public function setMsgHead()
    {
        $this->byte->setBytePrev($this->getHeader().$this->getMsgId().$this->getMsgFrom().$this->getMsgParam1().$this->getMsgParam2());
    }

    public function sendMsg($topicName = '', $retry = true)
    {
        $result= \notifyKafka::sendMsg($this->byte->getByte(), $topicName, $retry);
        if (!$result) {
            \util\log\akcsLog::debug('phpsocket: CSocket: sendMsg failed--msgid'. $this->id);
            return;
        }
    }
}

class Byte
{

    //长度
    private $length = 0;
    private $byte = '';
    //操作码
    private $code;

    public function setBytePrev($content)
    {
        $this->byte = $content . $this->byte;
    }

    public function getByte()
    {
        return $this->byte;
    }

    public function getLength()
    {
        return $this->length;
    }

    public function writeChar($string, $size)
    {
        $this->byte .= pack('a' . $size, $string);
        $this->length += $size;
        return; //下面为V3.2使用的

        $strsize = strlen($string);
        if ($size <= $strsize) {
            exit('字符串超长');
        }
        $this->length += $strsize;
        $str = array_map('ord', str_split($string));
        foreach ($str as $vo) {
            $this->byte .= pack('c', $vo);
        }

        for ($i = 1; $i <= ($size - $strsize); $i++) {
            $this->byte .= pack('c', '0');
            $this->length++;
        }
    }

    public function writeInt($str)
    {
        $this->length += 4;
        $this->byte .= pack('N', $str);
    }

    public function writeShortInt($interge)
    {
        $this->length += 2;
        $this->byte .= pack('N', $interge);
    }

    public function writeProtobuf($string)
    {
        $this->byte = $string;
        $strsize = strlen($string);
        $this->length += $strsize;
    }
}