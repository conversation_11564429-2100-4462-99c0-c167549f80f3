#!/bin/bash
# ****************************************************************************
# Author        :   buhuai.su
# Last modified :   2022-08-05
# Filename      :   install.sh
# Version       :
# Description   :   build.sh 构建脚本
# Modifier      :
# ****************************************************************************

set -euo pipefail

PROJECT_PATH=$1        #git clone时项目路径
SRC_PATH=$2            #编译后代码存储路径

DOCKER_TAG=$3          #docker镜像tag号
CONTAINER_NAME=$4      #启动容器名称
MIDDLEWARE=$5         #部署中间件
BRANCH_OR_TAG=$8


SLIM_PATH=$PROJECT_PATH/slim-cloud-app
SLIM_ADAPT_PATH=$PROJECT_PATH/slim-backend-adapt

PACKAGE_PATH=$PROJECT_PATH/package

PACKAGE_SLIM_ADAPT_PATH=$PACKAGE_PATH/slim_backend_adapt

DEFINE_FILE_PATH="$SLIM_ADAPT_PATH"/define.php
# 提取最后一个 "_" 或 "-" 后面的部分
suffix=$(echo "$BRANCH_OR_TAG" | sed -E 's/.*[-_](.*)/\1/')

# 去掉所有的 "."，保留纯数字
version=$(echo "$suffix" | tr -d '.')

cat > "$DEFINE_FILE_PATH" <<-EOF
<?php

const BRANCH_OR_TAG = '${version}';

EOF


clean(){
    if [ -d $PACKAGE_PATH/slim ]; then 
        echo "清理上次部署文件"
        rm -rf $PACKAGE_PATH/slim; 
    fi
}

package_slim_adapt(){
    if [ ! -d $SLIM_ADAPT_PATH ]; then mkdir -p $SLIMSLIM_ADAPT_PATH_WEBMAN_PATH; fi
    if [ ! -d $PACKAGE_SLIM_ADAPT_PATH ]; then mkdir -p $PACKAGE_SLIM_ADAPT_PATH; fi

    echo "复制代码到package，准备部署"
    cp -rf $SLIM_ADAPT_PATH/* $PACKAGE_SLIM_ADAPT_PATH/

    #slim和slim_webman的notify/proto文件夹共用，维护一份即可
    cp -rf $SLIM_PATH/framework/notify/proto $PACKAGE_SLIM_ADAPT_PATH/app/notify
    
    #里面包含版本信息 不能上线
    rm $PACKAGE_SLIM_ADAPT_PATH/composer.json
    rm $PACKAGE_SLIM_ADAPT_PATH/composer.lock
}


clean

#生成proto
ts=$(date +%s.%N)
WORKSPACE_PATH=/opt/jenkins/workspace
image=registry.cn-hangzhou.aliyuncs.com/ak_system/app_backend_for_cicd:akcloud-1.0
docker run --rm --name "create_proto-$ts" -v $WORKSPACE_PATH:$WORKSPACE_PATH $image /bin/bash -x ${PROJECT_PATH}/shell/create_proto.sh ${SLIM_PATH}/framework/notify/proto

package_slim_adapt