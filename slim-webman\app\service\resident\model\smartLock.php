<?php

namespace resident\model;

// 获取所有lock
function getSmartLockList($nodeUUID)
{
    return \util\container\medooDb()->select(
        "SmartLock",
        ["UUID", "LastConnectedTime", "DeviceUUID", "AccountUUID", "Relay", "Name", "BatteryLevel", "WifiStatus","KeepAlive", "Model"],
        ["PersonalAccountUUID" => $nodeUUID]
    );
}

// 获取单个lock
function getSmartLockInfo($uuid)
{
    return \util\container\medooDb()->get(
        "SmartLock",
        ["UUID", "PersonalAccountUUID", "LastConnectedTime", "DeviceUUID", "AccountUUID", "Relay", "Name", "BatteryLevel", "WifiStatus","KeepAlive", "Model"],
        ["UUID" => $uuid]
    );
}