<?php
/**
 * @description
 * <AUTHOR>
 * @date 2022/6/8 10:00
 * @version V6.4
 * @lastEditor csc
 * @lastEditTime 2022/6/8 10:00
 * @lastVersion V6.4
 */

namespace util\container;

/**
 * @description 代替原本gApp全局变量
 * <AUTHOR> 2022/6/8 10:02 V6.4
 * @lastEditor csc 2022/6/8 10:02 V6.4
 */
class GlobalApp extends \ArrayObject
{
    private $dataContainer = [];
    public static $instance;

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (!self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    public function setDataContainer($dataContainer)
    {
        $this->dataContainer = $dataContainer;
    }

    public function set($key, $value)
    {
        $this->dataContainer[$key] = $value;
    }

    public function get($key)
    {
        if(array_key_exists($key, $this->dataContainer))
        {
            return $this->dataContainer[$key];
        }
        return "";
    }
}