<?php

namespace common\control;

require_once __DIR__ . "/../../common/model/faceMng.php";
require_once __DIR__ . "/../../../util/fdfsManage.php";
require_once __DIR__ . "/../../../util/alarmManage.php";

//czw 这个后面放在notify层
function uploadFaceNotify($userConf)
{
    $role = $userConf['Role'];
    $changeType = 0;
    $mac = "";
    $node = $userConf['Account'];
    if ($role == ROLE_TYPE_PERSONNAL_MASTER || $role == ROLE_TYPE_PERSONNAL_SLAVE) {  //个人主账号,个人从账号
        $changeType = APP_PERSONAL_UPLOAD_FACE_PIC;
        $insatllID = 0;
        webPersonalModifyNotify($changeType, $node, $mac, $insatllID);
    } elseif ($role == ROLE_TYPE_COMMUNITY_MASTER || $role == ROLE_TYPE_COMMUNITY_SLAVE) { //社区主账号,社区从账号
        $changeType = APP_COMM_UPLOAD_FACE_PIC;
        $communitid = $userConf['MngID'];
        $unitid = $userConf['UnitID'];
        $accounts[0] = $userConf['UserAccount'];
        $accounts[1] = $userConf['Account'];
        webCommunityModifyNotify($changeType, $node, $mac, $communitid, $unitid);
        WebCommunityAccountModifyNotify($communitid, $accounts, 0);
    } elseif ($role == ROLE_TYPE_COMMUNITY_PM) {
        $communitid = $userConf['MngID'];

        WebCommunityAccountModifyNotify($communitid, $userConf['UserAccount'], 0);
    }
}

function getFaceStatus($request, $response)
{
    $userData = \util\container\getUserData();
    $personalAccountID = $userData['UserAccountID'];
    $subAccount = $request->getQueryParams()['sub_account'];
    if (!empty($subAccount)) {
        $subUserInfo = \common\model\getProjectSubUserInfo($subAccount);
        $personalAccountID = $subUserInfo['ID'];
        if ($subUserInfo['ParentID'] != $userData['UserAccountID']) {
            return \util\response\setResponseMessage($response, ERR_CODE_FAILED, [], "Sub account not belong to the account");
        }
    }
    /**
     * 1存在人脸
     * 0不存在人脸
     */
    $data = \common\model\queryFaceMng($personalAccountID);
    if ($data) {
        $statusJson["status"] = "1";
    } else {
        $statusJson["status"] = "0";
    }

    return \util\response\setResponseMessage($response, ERR_CODE_SUCCESS, $statusJson);  
}