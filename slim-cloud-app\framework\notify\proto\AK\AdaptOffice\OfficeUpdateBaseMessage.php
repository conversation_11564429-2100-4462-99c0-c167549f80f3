<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: AK.AdaptOffice.proto

namespace AK\AdaptOffice;

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>AK.AdaptOffice.OfficeUpdateBaseMessage</code>
 */
class OfficeUpdateBaseMessage extends \Google\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>uint32 change_type = 1;</code>
     */
    private $change_type = 0;
    /**
     * Generated from protobuf field <code>string office_uuid = 2;</code>
     */
    private $office_uuid = '';
    /**
     * Generated from protobuf field <code>string trace_id = 3;</code>
     */
    private $trace_id = '';
    /**
     * Generated from protobuf field <code>string msg_uuid = 4;</code>
     */
    private $msg_uuid = '';
    protected $message_type;

    public function __construct() {
        \GPBMetadata\AKAdaptOffice::initOnce();
        parent::__construct();
    }

    /**
     * Generated from protobuf field <code>uint32 change_type = 1;</code>
     * @return int
     */
    public function getChangeType()
    {
        return $this->change_type;
    }

    /**
     * Generated from protobuf field <code>uint32 change_type = 1;</code>
     * @param int $var
     * @return $this
     */
    public function setChangeType($var)
    {
        GPBUtil::checkUint32($var);
        $this->change_type = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string office_uuid = 2;</code>
     * @return string
     */
    public function getOfficeUuid()
    {
        return $this->office_uuid;
    }

    /**
     * Generated from protobuf field <code>string office_uuid = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setOfficeUuid($var)
    {
        GPBUtil::checkString($var, True);
        $this->office_uuid = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string trace_id = 3;</code>
     * @return string
     */
    public function getTraceId()
    {
        return $this->trace_id;
    }

    /**
     * Generated from protobuf field <code>string trace_id = 3;</code>
     * @param string $var
     * @return $this
     */
    public function setTraceId($var)
    {
        GPBUtil::checkString($var, True);
        $this->trace_id = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string msg_uuid = 4;</code>
     * @return string
     */
    public function getMsgUuid()
    {
        return $this->msg_uuid;
    }

    /**
     * Generated from protobuf field <code>string msg_uuid = 4;</code>
     * @param string $var
     * @return $this
     */
    public function setMsgUuid($var)
    {
        GPBUtil::checkString($var, True);
        $this->msg_uuid = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>.AK.AdaptOffice.OfficeUpdateFileConfig file_update = 5;</code>
     * @return \AK\AdaptOffice\OfficeUpdateFileConfig
     */
    public function getFileUpdate()
    {
        return $this->readOneof(5);
    }

    /**
     * Generated from protobuf field <code>.AK.AdaptOffice.OfficeUpdateFileConfig file_update = 5;</code>
     * @param \AK\AdaptOffice\OfficeUpdateFileConfig $var
     * @return $this
     */
    public function setFileUpdate($var)
    {
        GPBUtil::checkMessage($var, \AK\AdaptOffice\OfficeUpdateFileConfig::class);
        $this->writeOneof(5, $var);

        return $this;
    }

    /**
     * Generated from protobuf field <code>.AK.Server.P2PMainRequestWriteUserinfo user_info = 6;</code>
     * @return \AK\Server\P2PMainRequestWriteUserinfo
     */
    public function getUserInfo()
    {
        return $this->readOneof(6);
    }

    /**
     * Generated from protobuf field <code>.AK.Server.P2PMainRequestWriteUserinfo user_info = 6;</code>
     * @param \AK\Server\P2PMainRequestWriteUserinfo $var
     * @return $this
     */
    public function setUserInfo($var)
    {
        GPBUtil::checkMessage($var, \AK\Server\P2PMainRequestWriteUserinfo::class);
        $this->writeOneof(6, $var);

        return $this;
    }

    /**
     * @return string
     */
    public function getMessageType()
    {
        return $this->whichOneof("message_type");
    }

}

