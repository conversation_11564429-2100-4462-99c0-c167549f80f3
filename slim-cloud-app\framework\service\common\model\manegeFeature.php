<?php

namespace common\model;

require_once __DIR__ . "/../../common/model/distributorInfo.php";

use PDO;

function checkCommunityFeaturePlan($mngID, $feature)
{
    $db = \util\container\getDb();
    $sth = $db->prepare("select FeatureID from ManageFeature where AccountID = :account_id");
    $sth->bindParam(':account_id', $mngID, PDO::PARAM_INT);
    $sth->execute();
    $result = $sth->fetch(PDO::FETCH_ASSOC);
    if ($result['FeatureID'] == 0) {
        if ($feature == FeatureItemPin || $feature == FeatureItemTempkey) {
            return true;
        }
        return false;
    }
    $featureid = $result['FeatureID'];

    $sth = $db->prepare("select Item from FeaturePlan where ID = :featureid");
    $sth->bindParam(':featureid', $featureid, PDO::PARAM_INT);
    $sth->execute();
    $result = $sth->fetch(PDO::FETCH_ASSOC);
    if ($result) {
        return \util\utility\switchHandle($result['Item'], $feature);
    }
    return false;
}

function checkShowFace($resultToken,  $featureExpire)
{
    $isShowFace = 1;
    if ($resultToken['Role'] == ROLE_TYPE_COMMUNITY_MASTER || $resultToken['Role'] == ROLE_TYPE_COMMUNITY_SLAVE) {
        if (!$featureExpire && checkCommunityFeaturePlan($resultToken['MngID'], FeatureItemFaceRec)) {
            $isShowFace = checkFaceFlag($resultToken['Account']);//没过期且有对应得权限则按照配置
        }
    }
    return $isShowFace;
}

function checkShowTmpkey($resultToken, $featureExpire)
{
    $isShowTmpkey = intval($resultToken['TempKeyPermission']);
    if ($resultToken['Role'] == ROLE_TYPE_PERSONNAL_MASTER || $resultToken['Role'] == ROLE_TYPE_PERSONNAL_SLAVE) {
        $isShowTmpkey = 1;
    } elseif ($resultToken['Role'] == ROLE_TYPE_COMMUNITY_MASTER || $resultToken['Role'] == ROLE_TYPE_COMMUNITY_SLAVE) {
        if (!$featureExpire && checkCommunityFeaturePlan($resultToken['MngID'], FeatureItemTempkey)) {
            $isShowTmpkey = $isShowTmpkey;//没过期且有对应得权限则按照配置
        } else {
            $isShowTmpkey = 1;
        }
    }
    return $isShowTmpkey;
}

function checkEnableThirdCamera($mngID, $featureExpire)
{
    $enableThirdCamera = 0;
    if (!$featureExpire && checkCommunityFeaturePlan($mngID, FeatureItemThirdCamera)) {
        $enableThirdCamera = 1;//没过期且在方案内开
    } else {
        $enableThirdCamera = 0;
    }

    return $enableThirdCamera;
}

function checkEnablePinConfig($resultToken, $communityInfoSwitch, $featureExpire)
{
    $enablePinConfig = 1;
    if ($resultToken['Role'] == ROLE_TYPE_COMMUNITY_MASTER || $resultToken['Role'] == ROLE_TYPE_COMMUNITY_SLAVE) {
        if (!$featureExpire && checkCommunityFeaturePlan($resultToken['MngID'], FeatureItemPin)) {
            $enablePinConfig =  \util\utility\switchHandle($communityInfoSwitch, DevSwitchEnablePinConfig);
        }
    }
    return $enablePinConfig;
}

function checkShowIDAccess($resultToken, $featureExpire)
{
    $isShowIDAccess = 1;
    //dis未开启直接返回
    if (!checkUserHaveIDAccess($resultToken))
    {
        return 0;
    }
    if ($resultToken['Role'] == ROLE_TYPE_COMMUNITY_MASTER || $resultToken['Role'] == ROLE_TYPE_COMMUNITY_SLAVE) {
        if (!$featureExpire && checkCommunityFeaturePlan($resultToken['MngID'], FeatureItemIDAccess)) {
            $isShowIDAccess = checkIDAccessFlag($resultToken['Account']);//没过期且有对应得权限则按照配置
        }
    }
    else {
        //其余角色不支持
        $isShowIDAccess = 0;
    }
    return $isShowIDAccess;
}