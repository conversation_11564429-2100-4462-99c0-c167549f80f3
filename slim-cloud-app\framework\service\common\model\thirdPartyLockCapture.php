<?php

namespace common\model;

use PDO;

// ThirdPartyLockCapture表的FailedType字段
const FAILED_TYPE_SUCCESS = 0;                          // FailedType字段，默认值0
const FAILED_TYPE_FAILED = 1;                           // FailedType字段，1=通用失败
const FAILED_TYPE_SALTO_TOKEN_EXPIRED = 2;              // FailedType字段，2=salto token expired
const FAILED_TYPE_SALTO_IQ_PIN_VERIFICATION_FAILED = 3; // FailedType字段，3=IQ PIN verification failed
const FAILED_TYPE_SALTO_ACCOUNT_BLACKLISTED = 4;        // FailedType字段，4=Account blacklisted
const FAILED_TYPE_SALTO_OUTSIDE_AUTHORIZED_TIME = 5;    // FailedType字段，5=Outside authorized time
const FAILED_TYPE_SALTO_IQ_OFFLINE = 6;                 // FailedType字段，6=IQ offline
const THIRD_LOCK_FAILED_TYPE_NO_CONNECT_GATEWAY = 7; 
const FAILED_TYPE_ITEC_REMOTE_OPEN_NOT_IN_ACCESSGROUP = 1127; // ITEC 开门权限组判断失败
const THIRD_LOCK_FAILED_TYPE_TTLOCK_NOT_EXIST = ********;  //TT锁被第三方删除后 app要弹窗提示

function addYaleLockCapture($yaleLockInfo, $captureType)
{
    $db = \util\container\getDB();
    $sth = $db->prepare("INSERT INTO ThirdPartyLockCapture (LockName,PersonalAccountUUID,Initiator,CaptureType,LockType) VALUES (:lock_name,:personal_uuid,'Door Sensor',:captur_type,:lock_type)");
    $sth->bindParam(':lock_name', $yaleLockInfo['LockName'], PDO::PARAM_STR);
    $sth->bindParam(':personal_uuid', $yaleLockInfo['PersonalAccountUUID'], PDO::PARAM_STR);
    $sth->bindParam(':captur_type', $captureType, PDO::PARAM_INT);
    $lockType = YALE_LOCK_TYPE;
    $sth->bindParam(':lock_type', $lockType, PDO::PARAM_INT);
    $sth->execute();
}