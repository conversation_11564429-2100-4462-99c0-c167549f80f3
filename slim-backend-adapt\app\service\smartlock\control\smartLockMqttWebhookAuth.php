<?php

namespace smartlock\control;

require_once __DIR__ . "/../../smartlock/model/smartLock.php";
require_once __DIR__ . "/../../../util/smartlockAdapt.php";

function mqttWebhookAuth($request, $response)
{
    $authInfo = $request->getParsedBody();

    //内部服务鉴权
    if($authInfo['username'] == CSSMARTLOCK_USERNAME && $authInfo['password'] == CSSMARTLOCK_PASSWORD)
    {
        $response = json(
            [
                'result' => "allow",
                'is_superuser' => false
            ]
        );

        return $response;
    }

    //终端锁鉴权
    $userParts = explode('-', $authInfo['username']);
    $deviceStyle = $userParts[0]; 
    $deviceMac = $userParts[1]; 
    if(
      ($deviceStyle ==  SMARTLOCK_MQTT_CLIENT_FLAG_SL20 && \smartlock\model\SmartLockMqttAuthCheck($deviceMac, $authInfo['client_id'], $authInfo['password']))
    ||($deviceStyle ==  SMARTLOCK_MQTT_CLIENT_FLAG_SL50 && \smartlock\model\SmartLockMqttAuthCheck($deviceMac, \util\smartlockAdapt\mqttClientToRealClient($authInfo['client_id']), $authInfo['password']))
    )
    {
        $response = json(
            [
                'result' => "allow",
                'is_superuser' => false
            ]
        );
        return $response;
    }
    else
    {
        \util\log\akcsLog::debug( "MqttAuthCheck error,auth info: " . json_encode($authInfo) );
        $response = json(
            [
                'result' => "deny",
                'is_superuser' => false
            ]
        );

        return $response;
    }
}