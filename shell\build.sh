#!/bin/bash
# ****************************************************************************
# Author        :   buhuai.su
# Last modified :   2022-08-05
# Filename      :   install.sh
# Version       :
# Description   :   build.sh 构建脚本
# Modifier      :
# ****************************************************************************

set -euo pipefail

PROJECT_PATH=$1        #git clone时项目路径
SRC_PATH=$2            #编译后代码存储路径

DOCKER_TAG=$3          #docker镜像tag号
CONTAINER_NAME=$4      #启动容器名称
MIDDLEWARE=$5         #部署中间件
BRANCH_OR_TAG=$8

if [ "$MIDDLEWARE" == "slim_backend_adapt" ];then
    source "$PROJECT_PATH"/shell/build_adapt.sh "$@"
    exit
fi


SLIM_PATH=$PROJECT_PATH/slim-cloud-app
SMARTHOME_PBX_API=$PROJECT_PATH/slim-pbx-api
SLIM_WEBMAN_PATH=$PROJECT_PATH/slim-webman
PACKAGE_PATH=$PROJECT_PATH/package

PACKAGE_SLIM_PATH=$PACKAGE_PATH/slim
PACKAGE_SMARTHOME_PBX_API_PATH=$PACKAGE_PATH/SmartHomePbxApi
PACKAGE_SLIM_WEBMAN_PATH=$PACKAGE_PATH/slim_webman

DEFINE_FILE_PATH="$SLIM_WEBMAN_PATH"/define.php
# 提取最后一个 "_" 或 "-" 后面的部分
suffix=$(echo "$BRANCH_OR_TAG" | sed -E 's/.*[-_](.*)/\1/')

# 去掉所有的 "."，保留纯数字
version=$(echo "$suffix" | tr -d '.')

cat > "$DEFINE_FILE_PATH" <<-EOF
<?php

const BRANCH_OR_TAG = '${version}';

EOF


clean(){
    if [ -d $PACKAGE_PATH/slim ]; then 
        echo "清理上次部署文件"
        rm -rf $PACKAGE_PATH/slim; 
    fi
}
package_slim(){
    if [ ! -d $SLIM_PATH ]; then mkdir -p $SLIM_PATH; fi
    if [ ! -d $PACKAGE_SLIM_PATH ]; then mkdir -p $PACKAGE_SLIM_PATH; fi

    echo "复制代码到package，准备部署"
    cp -rf $SLIM_PATH/* $PACKAGE_SLIM_PATH/
    #里面包含版本信息 不能上线
    rm $PACKAGE_SLIM_PATH/composer.json
    rm $PACKAGE_SLIM_PATH/composer.lock
}
package_smarthome_pbx_api(){
    if [ ! -d $SMARTHOME_PBX_API ]; then mkdir -p $SMARTHOME_PBX_API; fi
    if [ ! -d $PACKAGE_SMARTHOME_PBX_API_PATH ]; then mkdir -p $PACKAGE_SMARTHOME_PBX_API_PATH; fi

    echo "复制代码到package，准备部署"
    cp -rf $SMARTHOME_PBX_API/* $PACKAGE_SMARTHOME_PBX_API_PATH/
}
package_slim_webman(){
    if [ ! -d $SLIM_WEBMAN_PATH ]; then mkdir -p $SLIM_WEBMAN_PATH; fi
    if [ ! -d $PACKAGE_SLIM_WEBMAN_PATH ]; then mkdir -p $PACKAGE_SLIM_WEBMAN_PATH; fi

    echo "复制代码到package，准备部署"
    cp -rf $SLIM_WEBMAN_PATH/* $PACKAGE_SLIM_WEBMAN_PATH/

    #slim和slim_webman的notify/proto文件夹共用，维护一份即可
    cp -rf $SLIM_PATH/framework/notify/proto $PACKAGE_SLIM_WEBMAN_PATH/app/notify
    
    #里面包含版本信息 不能上线
    rm $PACKAGE_SLIM_WEBMAN_PATH/composer.json
    rm $PACKAGE_SLIM_WEBMAN_PATH/composer.lock
}

clean

if [ -z "$MIDDLEWARE" ];then
    echo "请选择需要部署的中间件";
    exit 1
fi

#生成proto
ts=$(date +%s.%N)
WORKSPACE_PATH=/opt/jenkins/workspace
image=registry.cn-hangzhou.aliyuncs.com/ak_system/app_backend_for_cicd:akcloud-1.0
docker run --rm --name "create_proto-$ts" -v $WORKSPACE_PATH:$WORKSPACE_PATH $image /bin/bash -x ${PROJECT_PATH}/shell/create_proto.sh ${SLIM_PATH}/framework/notify/proto

array=(${MIDDLEWARE//,/ })  
for var in ${array[@]}
do
    if [ "$var" == "slim" ];then
        package_slim
    elif [ "$var" == "smarthome_pbx_api" ];then
        package_smarthome_pbx_api
    elif [ "$var" == "slim_webman" ];then
        package_slim_webman
    fi
done