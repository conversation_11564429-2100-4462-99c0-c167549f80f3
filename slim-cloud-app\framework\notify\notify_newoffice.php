<?php

require_once(dirname(__FILE__).'/socket_newoffice.php');

/*******************************办公消息枚举*******************************/

function SendNewOfficeAppFeedbackEmail($emailInfo)
{
    //协议通用字段
    $datas['email'] = $emailInfo['email'];
    $datas['send_to_type'] = $emailInfo['send_to_type'];
    $datas['language'] = $emailInfo['language'];
    $datas['content'] = (string)$emailInfo['content'];
    $datas['contact_email'] = (string)$emailInfo['contact_email'];
    $datas['user'] = (string)$emailInfo['sip_account'];
    $datas['ins_account'] = (string)$emailInfo['ins_name'];
    $datas['file_list'] = (string)$emailInfo['file_list'];
    $datas['user_type'] = $emailInfo['user_type'];
    //适配cspush
    $datas['project_type'] = PROJECT_TYPE_NEW_OFFICE;
    $datas['email_type'] = "office_app_feedback";
    //新办公信息字段
    $datas['office_name'] = (string)$emailInfo['project_name'];
    $datas['company_name'] = (string)$emailInfo['company'];
    //发给dis的邮件需要携带dis信息，用于邮件标题，ins的已存在ins_account中了
    if($datas['send_to_type'] == "dis") {
        $datas['dis_account'] = (string)$emailInfo['dis_name'];
    }
    //内部邮件需要设置抄送列表
    if($datas['send_to_type'] == "company") {
        $datas['cc_list'] = (string)$emailInfo['cc_list'];
    }

    $datas['email_type'] = "office_app_feedback";

    \util\log\akcsLog::debug("[sendAppFeedbackEmail] : Send email = {email}", ['email' => $datas['email']]);

    $jsondata = \util\common\createGeneralMessage(
        "email",
        $datas
    );

    JsonMessageNotify(PROJECT_TYPE_NEW_OFFICE, $jsondata);
}

function JsonMessageNotify($from, $jsondata)
{
    $Socket = new CJsonMessageNotify();

    $Socket->setMsgID(0);
    $Socket->setMsgProjectType($from);
    $Socket->copy(json_encode($jsondata));
}
