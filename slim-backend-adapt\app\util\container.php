<?php

namespace util\container;

require_once(dirname(__FILE__).'/globalApp.php');


function medooDb(){
    $gContainer = GlobalApp::getInstance();
    return $gContainer->get('akcs_medoo_db');
}

function getRedis(){
    $gContainer = GlobalApp::getInstance();
    return $gContainer->get('akcs_redis');
}

function setUserData($data){
    $tmp_arr = [];
    $gContainer = GlobalApp::getInstance();
    $tmp_arr = $gContainer->get('akcs_user_data');
    if(!$tmp_arr) {
        $tmp_arr = array();
    }
    foreach($data as $key => $value){
        $tmp_arr[$key] = $value;
    }
    $gContainer->set('akcs_user_data', $tmp_arr);
}

function getUserData(){
    $gContainer = GlobalApp::getInstance();
    return $gContainer->get('akcs_user_data');
}

function getResponseData(){
    $gContainer = GlobalApp::getInstance();
    return $gContainer->get('akcs_response_data');
}

function setResponseData($data){
    $gContainer = GlobalApp::getInstance();
    $gContainer['akcs_response_data'] = $data;
}

function getTraceID(){
    $gContainer = GlobalApp::getInstance();
    return $gContainer->get('akcs_trace_id');
}