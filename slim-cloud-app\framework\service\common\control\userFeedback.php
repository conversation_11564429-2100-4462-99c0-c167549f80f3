<?php

namespace  common\control;

require_once __DIR__ . "/../../common/model/account.php";
require_once __DIR__ . "/../../common/model/customerService.php";
require_once __DIR__ . "/../../common/model/appFeedback.php";
require_once __DIR__ . "/../../common/model/communityUnit.php";
require_once __DIR__ . "/../../../util/s3Handle.php";
require_once __DIR__ . "/../../../util/common.php";

function UploadFeedbackFile($request, &$fileList)
{
    $fileList = "";

    //form-data文件处理
    $files = $request->getUploadedFiles();
    $time = time();
    $today = date("Ymd");
    $s3Cfg = new \util\s3\S3Handle();
    foreach ($files as $file) {
        if ($file->getError() !== UPLOAD_ERR_OK) {
            return -1;
        }
        $fileName = $file->getClientFilename();
        $fileRoot = FEEDBACK_SAVE_ROOT_DIR;
        $savepath = $fileRoot . "/" . $time . "_" . $fileName;
        $file->moveTo($savepath);
        $random = \util\common\randomkeys(16);
        $remotePath = "AppFeedBack/" . $today . "/" . $random;
        $innerFlag = 1; //获取内网url
        $url = $s3Cfg->UploadFile($remotePath, $savepath, $innerFlag); 
        if($fileList == "") {
            $fileList = $fileList . $url;
        } else {
            $fileList = $fileList . ";" . $url;
        }
        if (!unlink($savepath)) {
            \util\log\akcsLog::debug("Delete Local file fail");
        }
    }
}

function AppUserFeedback($request, $response)
{
    //发送方信息获取
    $userData = \util\container\getUserData();

    $fileList = "";
    if (0 != UploadFeedbackFile($request, $fileList))
    {
        return \util\response\setResponseMessage($response, ERR_CODE_FAILED, [] ,"You did not upload a picture");
    }
    
    //更新数据库表
    $feedbackUUID = \util\common\getMysqlUUID();
    $parsedBody = $request->getParsedBody();
    \common\model\addAppFeedback($userData['UUID'],$parsedBody['content'],$parsedBody['email'],$fileList,$feedbackUUID);    
    $emailInfo = [];
    //endUser具体类型判断
    if($userData['Role'] == ROLE_TYPE_PERSONNAL_MASTER || $userData['Role'] == ROLE_TYPE_PERSONNAL_SLAVE) {
        $emailInfo['user_type'] = USER_TYPE_SIG;
    } else if($userData['Role'] == ROLE_TYPE_COMMUNITY_MASTER || $userData['Role'] == ROLE_TYPE_COMMUNITY_SLAVE) {
        $emailInfo['user_type'] = USER_TYPE_RESIDENCE;
    } else if(\util\common\isOfficeUser($userData['Role'])) {
        $emailInfo['user_type'] = USER_TYPE_OFFICE;
    } else if($userData['Role'] == ROLE_TYPE_COMMUNITY_PM) {
        $emailInfo['user_type'] = USER_TYPE_COMMUNITY_PM;
    } else {
        return \util\response\setResponseMessage($response, ERR_CODE_FAILED, [] ,"user role feedback function unsupported.");
    }
    //根据MngUUID查ManageGroup
    $mngInfo = \common\model\getAccountByUUID($userData['MngUUID']);
    if(!$mngInfo) {
        return \util\response\setResponseMessage($response, ERR_CODE_FAILED, [] ,"ManageGroup not found");
    }
    //根据ManageGroup查ins
    $insAccountInfo = \common\model\getAccountByID($mngInfo['ManageGroup']);
    if(!$insAccountInfo) {
        return \util\response\setResponseMessage($response, ERR_CODE_FAILED, [] ,"Installer not found");
    }
    //根据UUID查sub-dis
    $subDisAccountInfo = \common\model\getSubDisAccountByInsUUID($insAccountInfo['UUID']);
    $subDisFlag = 1;
    if(!$subDisAccountInfo) {
        \util\log\akcsLog::debug("sub dis not found");
        $subDisFlag = 0;
    }
    //根据ParentUUID查dis
    $disAccountInfo = \common\model\getAccountByUUID($insAccountInfo['ParentUUID']);
    if(!$disAccountInfo) {
        return \util\response\setResponseMessage($response, ERR_CODE_FAILED, [] ,"Distributor not found.");
    }
    //通用字段
    $emailInfo['file_list'] = $fileList;
    $emailInfo['ins_name'] = $insAccountInfo['Account'];
    $emailInfo['sip_account'] = $userData['SipAccount'];
    $emailInfo['content'] = $parsedBody['content'];
    $emailInfo['contact_email'] = $parsedBody['email'];
    $emailInfo['email_type'] = "app_feedback"; 

    //社区用户字段
    if($emailInfo['user_type'] == USER_TYPE_RESIDENCE) {
        $emailInfo['project_name'] = $mngInfo['Location'];
        $emailInfo['apt'] = \common\model\getRoomNumberByRoomID($userData['RoomID']); 
        $building = \common\model\getBuildingNameByID($userData['UnitID']);
        if(!$building) {
            return \util\response\setResponseMessage($response, ERR_CODE_FAILED, [] ,"Community Building Name not found.");
        }
        $emailInfo['building'] = $building;
    }
    //办公用户字段 
    else if($emailInfo['user_type'] == USER_TYPE_OFFICE) {
        $emailInfo['project_name'] = $mngInfo['Location'];
        $office = \common\model\getBuildingNameByID($userData['UnitID']);
        if(!$office) {
            return \util\response\setResponseMessage($response, ERR_CODE_FAILED, [] ,"Office Name not found.");
        }
        $emailInfo['building'] = $office;
    }
    //pm APP用户字段
    else if($emailInfo['user_type'] == USER_TYPE_COMMUNITY_PM) {
        $emailInfo['project_name'] = $mngInfo['Location'];
    }
    //是否发给内部邮箱标志
    $innerFlag = 1;
    //发送邮件:1.ins(sub-ins) 2.dis 3.sub-dis 4.内部邮箱
    $contact_email = \common\model\checkFeedbackSwitchByAccount($insAccountInfo['Account']);
    if($contact_email) {
        $innerFlag = 0;
        //ins有开启开关，则填入email
        $emailInfo['email'] = $contact_email;
        $emailInfo['language'] = $insAccountInfo['Language'];
        $emailInfo['send_to_type'] = "ins";
        \common\model\addAppFeedbackReceiverList($insAccountInfo['Account'],$feedbackUUID);
        sendAppFeedbackEmail($emailInfo);
    }
    $contact_email = \common\model\checkFeedbackSwitchByAccount($disAccountInfo['Account']);
    if($contact_email) {
        $innerFlag = 0;
        //dis有开启开关，则填入email
        $emailInfo['email'] = $contact_email;
        $emailInfo['language'] = $disAccountInfo['Language'];
        $emailInfo['send_to_type'] = "dis";
        $emailInfo['dis_name'] = $disAccountInfo['Account'];
        \common\model\addAppFeedbackReceiverList($disAccountInfo['Account'],$feedbackUUID);
        sendAppFeedbackEmail($emailInfo);
    }
    if($subDisFlag == 1) {
        //有sub-dis，检查是否开启开关
        $contact_email = \common\model\checkFeedbackSwitchByAccount($subDisAccountInfo['Account']);
        if($contact_email) {
            $innerFlag = 0;
            $emailInfo['email'] = $contact_email;
            $emailInfo['language'] = $subDisAccountInfo['Language'];
            $emailInfo['send_to_type'] = "dis";
            $emailInfo['dis_name'] = $subDisAccountInfo['Account'];
            \common\model\addAppFeedbackReceiverList($subDisAccountInfo['Account'],$feedbackUUID);
            sendAppFeedbackEmail($emailInfo);            
        }
    }
    //内部邮箱,仅当上述角色都没开开关时才发
    if($innerFlag == 1) {
        $emailInfo['email'] = FEEDBACK_SEND_EMAIL_MAIN;
        $emailInfo['cc_list'] = implode(",", FEEDBACK_SNED_EMAIL_CC_LIST);
        $emailInfo['language'] = "en";
        $emailInfo['send_to_type'] = "company";
        sendAppFeedbackEmail($emailInfo);
    }

    return \util\response\setResponseMessage($response, ERR_CODE_SUCCESS);
}