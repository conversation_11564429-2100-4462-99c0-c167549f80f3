<?php

namespace util\route;


class RouteCollect
{
    private $routeList = [];
    private $projectType;

    public static $instance;

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (!self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    public function get($uri, $callback)
    {
        $this->routeList[$uri] = $callback;
    }
    public function post($uri, $callback)
    {
        $this->routeList[$uri] = $callback;
    }

    public function getCallback($uri)
    {
        return $this->routeList[$uri];
    }

    public function getAllUri()
    {
        return array_keys($this->routeList);
    }

}