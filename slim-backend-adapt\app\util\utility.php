<?php

namespace util\utility;

//用于凯撒的偏移列表,找到字符然后加上偏移得到最终的字符，这个要和C++的对应
const CONFUSE_OFFSET_STR = 'CADaHgKxWec5f2otYIiRlmNLqP0z3hU7ZnFbES8QTOs9dG6yMvu4Jp1VjkrBwX';
const CONFUSE_RANDOM_HEAD_LEN = 4;
const CONFUSE_PASSWORD_INDEX = 5;
const CONFUSE_PASSWORD_LEN = 2;
const CONFUSE_SETP_LEN = 1;//凯撒的偏移setp 用1 位表示
const CONFUSE_OUT_LEN = 32;
const CONFUSE_PREFIX_INDEX = 7;
const CONFUSE_PASSWD_MAX_LEN = 25;//32-7
const CONFUSE_PASSWD_MIN_LEN = 6;
const CONFUSE_OFFSET_STR_MAX_INDEX = 61;
const CONFUSE_OFFSET_STR_LEN = 62;


//中间件调用
function callMiddel($app, $callSeq) {
    //数组反转，先add的中间件 后执行
    $reverse=array_reverse($callSeq);
    foreach($reverse as $value) {
        $app -> add($value);
    }
}

function switchHandle($value, $pos)
{
    return ($value>>$pos)&1;
}

function getDecodeChar($char, $step, $offsetArr)
{
    $realCode = '';
    $index = array_search($char, $offsetArr);
    if ($index !== false) {
        $newIndex = $index - $step;
        if ($newIndex < 0) {
            $newIndex = $newIndex + CONFUSE_OFFSET_STR_LEN;
        }
        $realCode = CONFUSE_OFFSET_STR[$newIndex];
    } else {
        $realCode = $char;
    }
    return $realCode;
}

function passwdDecode($code)
{
    $length = strlen($code);
    if ($length != CONFUSE_OUT_LEN) {
        return $code;
    }

    $offsetArr = str_split(CONFUSE_OFFSET_STR, 1);
    //偏移位数
    $step = intval(substr($code, CONFUSE_RANDOM_HEAD_LEN, CONFUSE_SETP_LEN));

    //密码长度
    $tmpCodeLen = substr($code, CONFUSE_PASSWORD_INDEX, CONFUSE_PASSWORD_LEN);
    $codeLen = getDecodeChar($tmpCodeLen[0], $step, $offsetArr);
    $codeLen .= getDecodeChar($tmpCodeLen[1], $step, $offsetArr);
    $codeLen = intval($codeLen);
    //偏移后的密码
    $confusedCode = substr($code, CONFUSE_PREFIX_INDEX, $codeLen);

    $realCode = '';
    //对密码进行去混淆，特殊字符不处理
    for ($i=0; $i<$codeLen; $i++) {
        $realCode  .= getDecodeChar($confusedCode[$i], $step, $offsetArr);
    }
    return $realCode;
}

function createTraceID($len)
{
    $chars='ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789abcdefghijklmnopqrstuvwxyz';
    $string=time();
    for (; $len>=1; $len--) {
        $position=rand()%strlen($chars);
        $position2=rand()%strlen($string);
        $string=substr_replace($string, substr($chars, $position, 1), $position2, 0);
    }
    return $string;
}

function curlRequest($url, $data, $header)
{
    $curl = curl_init();
    curl_setopt($curl, CURLOPT_URL, $url);
    curl_setopt($curl, CURLOPT_HEADER, 0);
    curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($curl, CURLOPT_POST, 1);

    curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
    curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
    //超时，只需要设置一个秒的数量就可以
    curl_setopt($curl, CURLOPT_CONNECTTIMEOUT, 5);
    curl_setopt($curl, CURLOPT_TIMEOUT, 10);
    curl_setopt($curl, CURLOPT_IPRESOLVE, CURL_IPRESOLVE_V4);
    curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
    //设置header头
    if (!empty($header)) {
        curl_setopt($curl, CURLOPT_HTTPHEADER, $header);
    }
    $data = curl_exec($curl);
    $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
    curl_close($curl);
    if ($httpCode != 200) {
        \util\log\akcsLog::debug("http code is $httpCode not 200. error is " . var_export(curl_error($curl), true));
        return -1;
    }
    return 0;
}

function setSwitchHandle($bits, $number = 0) {
    // 如果$bits不是数组，将其转换为数组
    if (!is_array($bits)) {
        $bits = [$bits];
    }
    foreach ($bits as $bit) {
        $number |= 1 << $bit;
    }

    return $number;
}

/**
 * 将二进制位值转换为位置索引
 * 例如: 1->0, 2->1, 4->2, 8->3 等
 */
function bitValueToIndex($bitValue) {
    
    $index = 0;
    while (($bitValue & 1) == 0) {
        $bitValue >>= 1;
        $index++;
    }
    
    return $index;
}